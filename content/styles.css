/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    width: 100%;
    overflow: hidden;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
    display: flex;
    justify-content: center;
    align-items: center;
}

.container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100vw;
    height: 100vh;
    padding: 20px;
}

/* FireRok Image Styling */
.firerok-image {
    /* Responsive scaling */
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;

    /* Center the image */
    display: block;
    margin: 0 auto;

    /* Maintain aspect ratio */
    object-fit: contain;

    /* Smooth scaling */
    transition: all 0.3s ease;

    /* Remove any borders */
    border: none;
    outline: none;
}

/* Size constraints for different screen sizes */
@media (min-width: 1920px) {
    .firerok-image {
        max-width: 80vw;
        max-height: 80vh;
    }
}

@media (max-width: 1919px) and (min-width: 1200px) {
    .firerok-image {
        max-width: 85vw;
        max-height: 85vh;
    }
}

@media (max-width: 1199px) and (min-width: 768px) {
    .firerok-image {
        max-width: 90vw;
        max-height: 90vh;
    }
}

@media (max-width: 767px) and (min-width: 480px) {
    .firerok-image {
        max-width: 95vw;
        max-height: 95vh;
    }
}

@media (max-width: 479px) {
    .firerok-image {
        max-width: 98vw;
        max-height: 98vh;
    }

    .container {
        padding: 10px;
    }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 600px) {
    .firerok-image {
        max-height: 95vh;
        max-width: 70vw;
    }
}

/* Portrait orientation adjustments */
@media (orientation: portrait) and (max-width: 600px) {
    .firerok-image {
        max-width: 95vw;
        max-height: 70vh;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .firerok-image {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print styles */
@media print {
    body {
        background: white;
    }

    .firerok-image {
        max-width: 100%;
        max-height: 100%;
        page-break-inside: avoid;
    }
}

/* Accessibility - Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .firerok-image {
        transition: none;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #0a0a0a 0%, #1d1d1d 50%, #0a0a0a 100%);
    }
}

/* Light mode support */
@media (prefers-color-scheme: light) {
    body {
        background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 50%, #f5f5f5 100%);
    }
}