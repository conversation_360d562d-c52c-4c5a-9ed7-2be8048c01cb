* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a1a0a 100%);
    color: white;
    height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.container {
    position: relative;
    text-align: center;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

#fireCanvas {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 10px;
    box-shadow: 0 0 50px rgba(255, 100, 0, 0.3);
    background: transparent;
}

.content {
    position: relative;
    z-index: 10;
    margin-top: 2rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.content h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ff4500, #ffd700, #ff6a33);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: glow 2s ease-in-out infinite alternate;
}

.content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-button {
    padding: 1rem 2rem;
    font-size: 1.2rem;
    background: linear-gradient(45deg, #ff4500, #ff6a33);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 69, 0, 0.4);
    text-transform: uppercase;
    font-weight: bold;
    letter-spacing: 1px;
}

.cta-button:hover {
    background: linear-gradient(45deg, #ff6a33, #ff8c00);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 69, 0, 0.6);
}

@keyframes glow {
    0% {
        text-shadow: 0 0 10px rgba(255, 69, 0, 0.8);
    }
    100% {
        text-shadow: 0 0 20px rgba(255, 215, 0, 0.8), 0 0 30px rgba(255, 69, 0, 0.6);
    }
}