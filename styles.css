* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: #1a1a1a;
    color: white;
    height: 100vh;
    overflow: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center;
}

h1 {
    margin-bottom: 2rem;
}

.animation-container {
    position: relative;
    height: 300px;
    margin: 2rem auto;
}

.rock {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 150px;
    height: 120px;
    background:
        radial-gradient(ellipse at 30% 20%, #8B7355 0%, #6B5B47 30%, #4A4035 60%, #2D2520 100%),
        linear-gradient(135deg, #7A6B57 0%, #5C4F42 50%, #3E342A 100%);
    border-radius: 45% 35% 25% 30%;
    box-shadow:
        inset -10px -10px 20px rgba(0,0,0,0.4),
        inset 5px 5px 15px rgba(139,115,85,0.3),
        0 15px 30px rgba(0,0,0,0.5),
        0 5px 10px rgba(0,0,0,0.3);
    animation: float 3s ease-in-out infinite;
    position: relative;
    overflow: hidden;
}

.rock::before {
    content: '';
    position: absolute;
    top: 15%;
    left: 20%;
    width: 60%;
    height: 40%;
    background: radial-gradient(ellipse, rgba(139,115,85,0.6) 0%, transparent 70%);
    border-radius: 50%;
    transform: rotate(-15deg);
}

.rock::after {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    background:
        radial-gradient(circle at 70% 30%, rgba(160,140,120,0.3) 0%, transparent 50%),
        radial-gradient(circle at 20% 70%, rgba(100,85,70,0.4) 0%, transparent 40%),
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 2px,
            rgba(0,0,0,0.1) 2px,
            rgba(0,0,0,0.1) 3px
        );
    border-radius: 40% 30% 20% 25%;
    pointer-events: none;
}

.fire-container {
    position: absolute;
    bottom: 80%;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 100px;
    filter: drop-shadow(0 0 20px rgba(255, 100, 0, 0.8));
}

.fire {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 25px;
    height: 70px;
    background:
        radial-gradient(ellipse at bottom, #ff0000 0%, #ff4500 25%, #ff8c00 50%, #ffd700 75%, #ffff99 100%);
    border-radius: 60% 60% 60% 60% / 70% 70% 30% 30%;
    filter: blur(1px);
    opacity: 0.95;
    animation: flicker 0.3s ease-in-out infinite alternate;
    box-shadow:
        0 0 10px rgba(255, 69, 0, 0.8),
        0 0 20px rgba(255, 140, 0, 0.6),
        0 0 30px rgba(255, 215, 0, 0.4);
}

.fire::before {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 15px;
    background: radial-gradient(ellipse, #ff0000 0%, #ff4500 50%, transparent 100%);
    border-radius: 50%;
    filter: blur(2px);
}

.fire:nth-child(2) {
    left: 25%;
    height: 55px;
    width: 20px;
    animation-delay: 0.1s;
    transform: translateX(-50%) rotate(-5deg);
}

.fire:nth-child(3) {
    left: 75%;
    height: 45px;
    width: 18px;
    animation-delay: 0.2s;
    transform: translateX(-50%) rotate(8deg);
}

.fire:nth-child(4) {
    left: 40%;
    height: 35px;
    width: 15px;
    animation-delay: 0.15s;
    transform: translateX(-50%) rotate(-3deg);
    opacity: 0.8;
}

.fire:nth-child(5) {
    left: 60%;
    height: 40px;
    width: 16px;
    animation-delay: 0.25s;
    transform: translateX(-50%) rotate(5deg);
    opacity: 0.85;
}

.particles-container,
.smoke-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.content {
    margin-top: 3rem;
}

.cta-button {
    margin-top: 2rem;
    padding: 1rem 2rem;
    font-size: 1.2rem;
    background: #ff4500;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s;
}

.cta-button:hover {
    background: #ff6a33;
}

@keyframes float {
    0%, 100% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    25% {
        transform: translate(-50%, -55%) rotate(0.5deg);
    }
    50% {
        transform: translate(-50%, -60%) rotate(0deg);
    }
    75% {
        transform: translate(-50%, -55%) rotate(-0.5deg);
    }
}

@keyframes flicker {
    0% {
        height: 70px;
        opacity: 0.95;
        transform: translateX(-50%) scaleX(1) scaleY(1);
    }
    25% {
        height: 65px;
        opacity: 0.9;
        transform: translateX(-50%) scaleX(0.95) scaleY(1.1);
    }
    50% {
        height: 75px;
        opacity: 1;
        transform: translateX(-50%) scaleX(1.05) scaleY(0.9);
    }
    75% {
        height: 60px;
        opacity: 0.85;
        transform: translateX(-50%) scaleX(0.9) scaleY(1.15);
    }
    100% {
        height: 70px;
        opacity: 0.95;
        transform: translateX(-50%) scaleX(1) scaleY(1);
    }
}

/* Add particle effects */
.particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: radial-gradient(circle, #ff4500 0%, #ff8c00 50%, transparent 100%);
    border-radius: 50%;
    pointer-events: none;
    animation: particle-rise 2s linear infinite;
}

@keyframes particle-rise {
    0% {
        opacity: 1;
        transform: translateY(0) translateX(0) scale(1);
    }
    50% {
        opacity: 0.7;
        transform: translateY(-50px) translateX(10px) scale(0.8);
    }
    100% {
        opacity: 0;
        transform: translateY(-100px) translateX(-5px) scale(0.3);
    }
}

/* Smoke effect */
.smoke {
    position: absolute;
    width: 40px;
    height: 40px;
    background: radial-gradient(circle, rgba(100, 100, 100, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    filter: blur(8px);
    animation: smoke-rise 4s linear infinite;
    pointer-events: none;
}

@keyframes smoke-rise {
    0% {
        opacity: 0.6;
        transform: translateY(0) translateX(0) scale(0.5);
    }
    50% {
        opacity: 0.3;
        transform: translateY(-80px) translateX(15px) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-160px) translateX(-10px) scale(1.5);
    }
}