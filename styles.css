* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: #1a1a1a;
    color: white;
    height: 100vh;
    overflow: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center;
}

h1 {
    margin-bottom: 2rem;
}

.animation-container {
    position: relative;
    height: 300px;
    margin: 2rem auto;
}

.rock {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 150px;
    height: 120px;
    background: #555;
    border-radius: 40% 40% 20% 20%;
    box-shadow: 0 10px 20px rgba(0,0,0,0.3);
    animation: float 3s ease-in-out infinite;
}

.fire-container {
    position: absolute;
    bottom: 80%;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 80px;
}

.fire {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 60px;
    background: linear-gradient(to top, #ff4500, #ffa500, #ffff00);
    border-radius: 50% 50% 20% 20%;
    filter: blur(2px);
    opacity: 0.9;
    animation: flicker 0.5s ease-in-out infinite alternate;
}

.fire:nth-child(2) {
    left: 30%;
    height: 50px;
    animation-delay: 0.1s;
}

.fire:nth-child(3) {
    left: 70%;
    height: 40px;
    animation-delay: 0.2s;
}

.content {
    margin-top: 3rem;
}

.cta-button {
    margin-top: 2rem;
    padding: 1rem 2rem;
    font-size: 1.2rem;
    background: #ff4500;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s;
}

.cta-button:hover {
    background: #ff6a33;
}

@keyframes float {
    0%, 100% {
        transform: translate(-50%, -50%);
    }
    50% {
        transform: translate(-50%, -60%);
    }
}

@keyframes flicker {
    0%, 100% {
        height: 60px;
        opacity: 0.9;
    }
    50% {
        height: 55px;
        opacity: 0.8;
    }
}