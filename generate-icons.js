#!/usr/bin/env node

/**
 * Icon Generator Script for FireRok
 * 
 * This script generates all the necessary icon files from the FireRok.svg
 * Run with: node generate-icons.js
 * 
 * Requirements: 
 * - npm install sharp (for PNG generation)
 * - or use online converters if sharp is not available
 */

const fs = require('fs');
const path = require('path');

// Icon sizes needed for different platforms
const iconSizes = [
    // Standard favicons
    { name: 'favicon-16x16.png', size: 16 },
    { name: 'favicon-32x32.png', size: 32 },
    
    // Apple Touch Icons
    { name: 'apple-touch-icon-57x57.png', size: 57 },
    { name: 'apple-touch-icon-60x60.png', size: 60 },
    { name: 'apple-touch-icon-72x72.png', size: 72 },
    { name: 'apple-touch-icon-76x76.png', size: 76 },
    { name: 'apple-touch-icon-114x114.png', size: 114 },
    { name: 'apple-touch-icon-120x120.png', size: 120 },
    { name: 'apple-touch-icon-144x144.png', size: 144 },
    { name: 'apple-touch-icon-152x152.png', size: 152 },
    { name: 'apple-touch-icon.png', size: 180 },
    
    // Android Chrome Icons
    { name: 'android-chrome-192x192.png', size: 192 },
    { name: 'android-chrome-512x512.png', size: 512 },
    
    // Microsoft Tiles
    { name: 'mstile-70x70.png', size: 70 },
    { name: 'mstile-144x144.png', size: 144 },
    { name: 'mstile-150x150.png', size: 150 },
    { name: 'mstile-310x150.png', size: 310, height: 150 },
    { name: 'mstile-310x310.png', size: 310 }
];

async function generateIcons() {
    console.log('🔥 FireRok Icon Generator');
    console.log('========================');
    
    // Check if FireRok.svg exists
    if (!fs.existsSync('FireRok.svg')) {
        console.error('❌ FireRok.svg not found in current directory');
        process.exit(1);
    }
    
    try {
        // Try to use sharp for PNG generation
        const sharp = require('sharp');
        console.log('✅ Sharp found - generating PNG icons...');
        
        for (const icon of iconSizes) {
            const width = icon.size;
            const height = icon.height || icon.size;
            
            await sharp('FireRok.svg')
                .resize(width, height)
                .png()
                .toFile(icon.name);
                
            console.log(`✅ Generated ${icon.name} (${width}x${height})`);
        }
        
        console.log('\n🎉 All icons generated successfully!');
        
    } catch (error) {
        if (error.code === 'MODULE_NOT_FOUND') {
            console.log('⚠️  Sharp not found. Install with: npm install sharp');
            console.log('\n📝 Alternative: Use online SVG to PNG converters');
            console.log('   Recommended: https://convertio.co/svg-png/');
            console.log('\n📋 Required icon sizes:');
            
            iconSizes.forEach(icon => {
                const size = icon.height ? `${icon.size}x${icon.height}` : `${icon.size}x${icon.size}`;
                console.log(`   - ${icon.name}: ${size}px`);
            });
        } else {
            console.error('❌ Error generating icons:', error.message);
        }
    }
}

// Instructions for manual generation
function showManualInstructions() {
    console.log('\n📖 Manual Icon Generation Instructions:');
    console.log('=====================================');
    console.log('1. Open FireRok.svg in a graphics editor (Inkscape, Illustrator, etc.)');
    console.log('2. Export as PNG with the following sizes:');
    console.log('');
    
    iconSizes.forEach(icon => {
        const size = icon.height ? `${icon.size}x${icon.height}` : `${icon.size}x${icon.size}`;
        console.log(`   ${icon.name}: ${size}px`);
    });
    
    console.log('\n3. Save all files in the same directory as index.html');
    console.log('4. The SVG will be used as the primary favicon for modern browsers');
}

// Run the generator
if (require.main === module) {
    generateIcons().then(() => {
        showManualInstructions();
    });
}

module.exports = { generateIcons, iconSizes };
