# FireRok 🔥🪨

A responsive web page that displays the FireRok.svg image with automatic scaling for any browser or screen size, complete with platform-specific icons and PWA support.

## Features

✅ **Responsive Design**: Automatically scales to fit any screen size  
✅ **Cross-Platform Icons**: Supports all major platforms and devices  
✅ **PWA Ready**: Web app manifest for mobile installation  
✅ **Modern Standards**: Uses SVG as primary favicon with PNG fallbacks  
✅ **Accessibility**: Supports reduced motion and color scheme preferences  

## Quick Start

1. **Open the page**: Simply open `index.html` in any web browser
2. **Generate icons** (optional): Run `node generate-icons.js` to create PNG icons
3. **Test icons** (optional): Open `icon-test.html` to verify icon generation
4. **Deploy**: Upload all files to your web server

## File Structure

```
FireRok/
├── index.html              # Main HTML page
├── styles.css              # Responsive CSS styles
├── FireRok.svg             # Main SVG image (used as favicon)
├── icon-test.html          # Icon verification test page
├── site.webmanifest        # Web app manifest
├── browserconfig.xml       # Microsoft tile configuration
├── generate-icons.js       # Icon generation script
└── README.md              # This file
```

## Icon Generation

### Automatic (Recommended)
```bash
# Install sharp for PNG generation
npm install sharp

# Generate all required icons
node generate-icons.js
```

**Features:**
- ✅ **Preserves full image** - No cropping, adds padding instead
- ✅ **Smart backgrounds** - Transparent for favicons, solid for Apple/Microsoft
- ✅ **Aspect ratio maintained** - Your FireRok image stays proportional
- ✅ **Platform optimized** - Different background strategies per platform

**Background Strategy:**
- **Favicons & Android**: Transparent background
- **Apple Touch Icons**: Dark solid background (Apple's recommendation)
- **Microsoft Tiles**: Dark solid background (better visibility)

### Manual
If you prefer manual generation, export FireRok.svg as PNG files with these sizes:

**Important:** When exporting manually, ensure you:
- ✅ **Preserve aspect ratio** - Don't crop the image
- ✅ **Add padding** instead of stretching to fit square dimensions
- ✅ **Use transparent background** for favicons and Android icons
- ✅ **Use solid background** for Apple and Microsoft icons

**Standard Favicons:**
- favicon-16x16.png (16×16px)
- favicon-32x32.png (32×32px)

**Apple Touch Icons:**
- apple-touch-icon.png (180×180px)
- apple-touch-icon-152x152.png (152×152px)
- apple-touch-icon-144x144.png (144×144px)
- apple-touch-icon-120x120.png (120×120px)
- apple-touch-icon-114x114.png (114×114px)
- apple-touch-icon-76x76.png (76×76px)
- apple-touch-icon-72x72.png (72×72px)
- apple-touch-icon-60x60.png (60×60px)
- apple-touch-icon-57x57.png (57×57px)

**Android Chrome Icons:**
- android-chrome-192x192.png (192×192px)
- android-chrome-512x512.png (512×512px)

**Microsoft Tiles:**
- mstile-70x70.png (70×70px)
- mstile-144x144.png (144×144px)
- mstile-150x150.png (150×150px)
- mstile-310x150.png (310×150px)
- mstile-310x310.png (310×310px)

## Responsive Breakpoints

The image automatically scales based on screen size:

- **4K+ (1920px+)**: 80% of viewport
- **Desktop (1200-1919px)**: 85% of viewport  
- **Tablet (768-1199px)**: 90% of viewport
- **Mobile (480-767px)**: 95% of viewport
- **Small Mobile (<480px)**: 98% of viewport

## Browser Support

- ✅ Chrome/Edge (SVG favicon + responsive design)
- ✅ Firefox (SVG favicon + responsive design)
- ✅ Safari (PNG fallback + responsive design)
- ✅ Mobile browsers (Touch icons + responsive design)
- ✅ Internet Explorer 11+ (PNG fallback)

## Customization

### Change Page Background
Edit the `background` property in `styles.css`:

```css
body {
    background: linear-gradient(135deg, #your-color 0%, #your-color2 100%);
}
```

### Change Icon Background Color
Edit the `BACKGROUND_COLOR` in `generate-icons.js` and regenerate:

```javascript
// Dark theme (default)
const BACKGROUND_COLOR = { r: 26, g: 26, b: 26, alpha: 1 };

// White background
const BACKGROUND_COLOR = { r: 255, g: 255, b: 255, alpha: 1 };

// FireRok orange background
const BACKGROUND_COLOR = { r: 218, g: 83, b: 44, alpha: 1 };
```

Then run: `node generate-icons.js`

### Adjust Scaling
Modify the media queries in `styles.css` to change how the image scales:

```css
@media (max-width: 768px) {
    .firerok-image {
        max-width: 90vw; /* Adjust this value */
        max-height: 90vh;
    }
}
```

### Theme Colors
Update the theme colors in `index.html` and `site.webmanifest`:

```html
<meta name="theme-color" content="#your-color">
```

## Deployment

1. Upload all files to your web server
2. Ensure the server serves SVG files with correct MIME type
3. Test on various devices and browsers

## License

This project is open source. Feel free to use and modify as needed.

---

**Made with 🔥 for FireRok**
