<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireRok Icon Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .icon-item {
            background: #2d2d2d;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .icon-item img {
            display: block;
            margin: 0 auto 10px;
            border: 1px solid #444;
            background: white;
        }
        .icon-item h3 {
            margin: 0 0 5px;
            font-size: 14px;
        }
        .icon-item p {
            margin: 0;
            font-size: 12px;
            color: #ccc;
        }
        .original {
            text-align: center;
            margin: 20px 0;
        }
        .original img {
            max-width: 200px;
            height: auto;
            border: 1px solid #444;
        }
        .comparison {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .comparison-item {
            text-align: center;
            background: #2d2d2d;
            padding: 15px;
            border-radius: 8px;
        }
        .comparison-item img {
            display: block;
            margin: 0 auto 10px;
            border: 1px solid #444;
        }
        .comparison-item h4 {
            margin: 0 0 5px;
            font-size: 14px;
        }
        .comparison-item p {
            margin: 0;
            font-size: 12px;
            color: #ccc;
        }
    </style>
</head>
<body>
    <h1>🔥 FireRok Icon Generation Test</h1>
    <p>This page shows the generated icons to verify they preserve the full image with padding instead of cropping.</p>
    <p><strong>Background Strategy:</strong> Transparent for favicons/Android, dark gray for Apple/Microsoft (blends with image content).</p>
    
    <div class="original">
        <h2>Original SVG</h2>
        <img src="FireRok.svg" alt="Original FireRok SVG">
        <p>Original aspect ratio preserved</p>
    </div>

    <h2>🎨 Background Color Strategy</h2>
    <p>Notice how the dark gray background (RGB: 64,64,64) blends naturally with the image content, especially around the white/transparent flame areas.</p>

    <div class="comparison">
        <div class="comparison-item">
            <img src="apple-touch-icon.png" alt="Dark Gray Background" style="background: #404040;">
            <h4>✅ Dark Gray Background</h4>
            <p>Blends with image content</p>
        </div>
        <div class="comparison-item">
            <img src="apple-touch-icon.png" alt="Black Background" style="background: #000000;">
            <h4>❌ Black Background</h4>
            <p>Creates harsh contrast</p>
        </div>
        <div class="comparison-item">
            <img src="android-chrome-192x192.png" alt="Transparent Background" style="background: white;">
            <h4>✅ Transparent Background</h4>
            <p>For modern browsers</p>
        </div>
    </div>

    <h2>Generated Icons</h2>
    <div class="icon-grid">
        <div class="icon-item">
            <img src="favicon-16x16.png" alt="Favicon 16x16">
            <h3>Favicon 16x16</h3>
            <p>Transparent background</p>
        </div>
        
        <div class="icon-item">
            <img src="favicon-32x32.png" alt="Favicon 32x32">
            <h3>Favicon 32x32</h3>
            <p>Transparent background</p>
        </div>
        
        <div class="icon-item">
            <img src="apple-touch-icon-76x76.png" alt="Apple Touch Icon 76x76">
            <h3>Apple Touch 76x76</h3>
            <p>Dark gray background</p>
        </div>

        <div class="icon-item">
            <img src="apple-touch-icon-120x120.png" alt="Apple Touch Icon 120x120">
            <h3>Apple Touch 120x120</h3>
            <p>Dark gray background</p>
        </div>

        <div class="icon-item">
            <img src="apple-touch-icon.png" alt="Apple Touch Icon 180x180">
            <h3>Apple Touch 180x180</h3>
            <p>Dark gray background</p>
        </div>
        
        <div class="icon-item">
            <img src="android-chrome-192x192.png" alt="Android Chrome 192x192">
            <h3>Android Chrome 192x192</h3>
            <p>Transparent background</p>
        </div>
        
        <div class="icon-item">
            <img src="android-chrome-512x512.png" alt="Android Chrome 512x512">
            <h3>Android Chrome 512x512</h3>
            <p>Transparent background</p>
        </div>
        
        <div class="icon-item">
            <img src="mstile-150x150.png" alt="Microsoft Tile 150x150">
            <h3>MS Tile 150x150</h3>
            <p>Dark gray background</p>
        </div>

        <div class="icon-item">
            <img src="mstile-310x150.png" alt="Microsoft Tile 310x150">
            <h3>MS Tile 310x150</h3>
            <p>Dark gray background (wide)</p>
        </div>
    </div>
    
    <h2>✅ Verification Checklist</h2>
    <ul>
        <li>All icons should show the complete FireRok image</li>
        <li>No cropping of the top or bottom of the image</li>
        <li>Dark gray padding added to sides (blends with image content)</li>
        <li>Apple/Microsoft icons have dark gray backgrounds (not black bars)</li>
        <li>Favicon/Android icons have transparent backgrounds</li>
        <li>Wide tile (310x150) shows full image with side padding</li>
        <li>Background color complements the white/transparent areas in the image</li>
    </ul>
</body>
</html>
