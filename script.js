class FireRockScene {
    constructor() {
        this.scene = new THREE.Scene();
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.clock = new THREE.Clock();
        this.particles = [];
        this.fireParticles = [];

        this.init();
        this.createRock();
        this.createFire();
        this.setupLighting();
        this.setupControls();
        this.animate();
    }

    init() {
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x2a3f5f); // Dark blue-gray background
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        document.getElementById('container').appendChild(this.renderer.domElement);

        this.camera.position.set(0, 2, 8);
        this.camera.lookAt(0, 0, 0);

        window.addEventListener('resize', () => this.onWindowResize());
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }

    createRock() {
        // Create rock geometry with noise for realistic shape
        const geometry = new THREE.SphereGeometry(1.5, 32, 32);

        // Add noise to vertices for irregular rock shape
        const vertices = geometry.attributes.position.array;
        for (let i = 0; i < vertices.length; i += 3) {
            const vertex = new THREE.Vector3(vertices[i], vertices[i + 1], vertices[i + 2]);
            const noise = (Math.random() - 0.5) * 0.3;
            vertex.normalize().multiplyScalar(1.5 + noise);
            vertices[i] = vertex.x;
            vertices[i + 1] = vertex.y;
            vertices[i + 2] = vertex.z;
        }
        geometry.attributes.position.needsUpdate = true;
        geometry.computeVertexNormals();

        // Create realistic rock material
        const material = new THREE.MeshLambertMaterial({
            color: 0x8B7355, // Lighter brown color
        });

        this.rock = new THREE.Mesh(geometry, material);
        this.rock.position.set(0, -0.5, 0);
        this.rock.castShadow = true;
        this.rock.receiveShadow = true;
        this.scene.add(this.rock);
    }

    createFire() {
        // Create fire particle system with larger, more visible particles
        const particleCount = 500;
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);
        const velocities = new Float32Array(particleCount * 3);
        const lifetimes = new Float32Array(particleCount);

        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;

            // Initial positions around the rock base
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * 0.8;
            positions[i3] = Math.cos(angle) * radius;
            positions[i3 + 1] = 0.8 + Math.random() * 0.2; // Start above rock
            positions[i3 + 2] = Math.sin(angle) * radius;

            // Fire colors (bright red to bright yellow)
            const intensity = Math.random();
            colors[i3] = 1; // Red
            colors[i3 + 1] = 0.3 + intensity * 0.7; // Green
            colors[i3 + 2] = intensity * 0.2; // Blue

            sizes[i] = Math.random() * 0.3 + 0.2; // Much larger particles

            // Initial velocities - upward with some spread
            velocities[i3] = (Math.random() - 0.5) * 0.03;
            velocities[i3 + 1] = Math.random() * 0.08 + 0.04; // Strong upward motion
            velocities[i3 + 2] = (Math.random() - 0.5) * 0.03;

            lifetimes[i] = Math.random() * 80 + 40;
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

        // Store for animation
        this.fireVelocities = velocities;
        this.fireLifetimes = lifetimes;
        this.maxLifetimes = [...lifetimes];

        // Create fire material with better visibility
        const material = new THREE.PointsMaterial({
            size: 0.5, // Larger base size
            vertexColors: true,
            transparent: true,
            opacity: 0.9,
            blending: THREE.AdditiveBlending,
            depthWrite: false,
            sizeAttenuation: true // Size based on distance
        });

        this.fireSystem = new THREE.Points(geometry, material);
        this.scene.add(this.fireSystem);
    }

    setupLighting() {
        // Brighter ambient light to see the rock
        const ambientLight = new THREE.AmbientLight(0x606060, 0.6);
        this.scene.add(ambientLight);

        // Fire light (orange/red)
        this.fireLight = new THREE.PointLight(0xff4500, 3, 15);
        this.fireLight.position.set(0, 2, 0);
        this.fireLight.castShadow = true;
        this.fireLight.shadow.mapSize.width = 1024;
        this.fireLight.shadow.mapSize.height = 1024;
        this.scene.add(this.fireLight);

        // Directional light for general illumination
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(5, 5, 5);
        directionalLight.castShadow = true;
        this.scene.add(directionalLight);

        // Additional fill light to illuminate the rock
        const fillLight = new THREE.DirectionalLight(0x87ceeb, 0.3);
        fillLight.position.set(-5, 3, -5);
        this.scene.add(fillLight);
    }

    setupControls() {
        // Mouse interaction
        this.mouse = new THREE.Vector2();

        document.addEventListener('mousemove', (event) => {
            this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        });

        document.addEventListener('click', () => {
            this.createExplosion();
        });
    }

    updateFire() {
        const positions = this.fireSystem.geometry.attributes.position.array;
        const colors = this.fireSystem.geometry.attributes.color.array;
        const sizes = this.fireSystem.geometry.attributes.size.array;

        for (let i = 0; i < positions.length / 3; i++) {
            const i3 = i * 3;

            // Update positions
            positions[i3] += this.fireVelocities[i3];
            positions[i3 + 1] += this.fireVelocities[i3 + 1];
            positions[i3 + 2] += this.fireVelocities[i3 + 2];

            // Add realistic fire turbulence
            positions[i3] += (Math.random() - 0.5) * 0.02;
            positions[i3 + 2] += (Math.random() - 0.5) * 0.02;

            // Slow down as particles rise (like real fire)
            this.fireVelocities[i3 + 1] *= 0.995;

            // Update lifetime
            this.fireLifetimes[i]--;

            // Reset particle if lifetime is over or too high
            if (this.fireLifetimes[i] <= 0 || positions[i3 + 1] > 4) {
                const angle = Math.random() * Math.PI * 2;
                const radius = Math.random() * 0.8;
                positions[i3] = Math.cos(angle) * radius;
                positions[i3 + 1] = 0.8 + Math.random() * 0.2;
                positions[i3 + 2] = Math.sin(angle) * radius;

                this.fireVelocities[i3] = (Math.random() - 0.5) * 0.03;
                this.fireVelocities[i3 + 1] = Math.random() * 0.08 + 0.04;
                this.fireVelocities[i3 + 2] = (Math.random() - 0.5) * 0.03;

                this.fireLifetimes[i] = this.maxLifetimes[i];
            }

            // Update color based on height and lifetime (fire gets yellower as it rises)
            const lifeRatio = this.fireLifetimes[i] / this.maxLifetimes[i];
            const height = Math.max(0, positions[i3 + 1] - 0.8);

            colors[i3] = 1; // Red stays constant
            colors[i3 + 1] = 0.3 + (height * 0.5) + (Math.random() * 0.2); // Green increases with height
            colors[i3 + 2] = Math.min(0.8, height * 0.3); // Blue for white-hot tips

            // Update size based on lifetime and height
            sizes[i] = (0.2 + Math.random() * 0.3) * lifeRatio * (1 - height * 0.3);
        }

        this.fireSystem.geometry.attributes.position.needsUpdate = true;
        this.fireSystem.geometry.attributes.color.needsUpdate = true;
        this.fireSystem.geometry.attributes.size.needsUpdate = true;

        // Animate fire light with more dramatic flickering
        this.fireLight.intensity = 2.5 + Math.sin(this.clock.getElapsedTime() * 15) * 0.8;
        this.fireLight.position.y = 2 + Math.sin(this.clock.getElapsedTime() * 8) * 0.3;
    }

    createExplosion() {
        // Create temporary explosion particles
        const explosionGeometry = new THREE.BufferGeometry();
        const explosionCount = 200;
        const explosionPositions = new Float32Array(explosionCount * 3);
        const explosionColors = new Float32Array(explosionCount * 3);

        for (let i = 0; i < explosionCount; i++) {
            const i3 = i * 3;

            explosionPositions[i3] = (Math.random() - 0.5) * 0.5;
            explosionPositions[i3 + 1] = 0.5;
            explosionPositions[i3 + 2] = (Math.random() - 0.5) * 0.5;

            explosionColors[i3] = 1;
            explosionColors[i3 + 1] = Math.random();
            explosionColors[i3 + 2] = 0;
        }

        explosionGeometry.setAttribute('position', new THREE.BufferAttribute(explosionPositions, 3));
        explosionGeometry.setAttribute('color', new THREE.BufferAttribute(explosionColors, 3));

        const explosionMaterial = new THREE.PointsMaterial({
            size: 0.2,
            vertexColors: true,
            transparent: true,
            blending: THREE.AdditiveBlending
        });

        const explosion = new THREE.Points(explosionGeometry, explosionMaterial);
        this.scene.add(explosion);

        // Remove explosion after animation
        setTimeout(() => {
            this.scene.remove(explosion);
        }, 2000);
    }

    animate() {
        requestAnimationFrame(() => this.animate());

        // Update fire particles
        this.updateFire();

        // Mouse interaction with rock
        if (this.rock) {
            this.rock.rotation.y += 0.005;
            this.rock.rotation.x = this.mouse.y * 0.1;
            this.rock.rotation.z = this.mouse.x * 0.1;
        }

        // Camera movement
        this.camera.position.x = Math.sin(this.clock.getElapsedTime() * 0.1) * 2;
        this.camera.position.z = 8 + Math.cos(this.clock.getElapsedTime() * 0.1) * 2;
        this.camera.lookAt(0, 0, 0);

        // Render the scene
        this.renderer.render(this.scene, this.camera);
    }
}

// Initialize the scene when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new FireRockScene();
});