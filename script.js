class FireRockScene {
    constructor() {
        this.scene = new THREE.Scene();
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.clock = new THREE.Clock();
        this.particles = [];
        this.fireParticles = [];

        this.init();
        this.createShaders();
        this.createRock();
        this.createFire();
        this.setupLighting();
        this.setupControls();
        this.animate();
    }

    createShaders() {
        // Fire particle vertex shader
        this.fireVertexShader = `
            attribute float size;
            attribute float lifetime;
            attribute vec3 velocity;

            uniform float time;
            uniform float pointSize;

            varying vec3 vColor;
            varying float vLifetime;

            void main() {
                vColor = color;
                vLifetime = lifetime;

                vec3 pos = position;

                // Add noise-based movement for realistic fire turbulence
                float noise = sin(pos.x * 10.0 + time * 3.0) * cos(pos.z * 8.0 + time * 2.0);
                pos.x += noise * 0.1;
                pos.z += noise * 0.08;

                vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
                gl_Position = projectionMatrix * mvPosition;

                // Size based on distance and lifetime
                gl_PointSize = size * pointSize * (300.0 / -mvPosition.z) * lifetime;
            }
        `;

        // Fire particle fragment shader
        this.fireFragmentShader = `
            varying vec3 vColor;
            varying float vLifetime;

            void main() {
                // Create circular particle shape
                vec2 center = gl_PointCoord - vec2(0.5);
                float dist = length(center);

                if (dist > 0.5) discard;

                // Create fire-like gradient from center
                float alpha = 1.0 - (dist * 2.0);
                alpha = pow(alpha, 2.0);

                // Add flickering effect
                alpha *= (0.8 + 0.2 * sin(gl_FragCoord.x * 0.1 + gl_FragCoord.y * 0.1));

                // Fade based on lifetime
                alpha *= vLifetime;

                // Enhance fire colors
                vec3 fireColor = vColor;
                fireColor.r = min(1.0, fireColor.r + 0.2);
                fireColor.g = min(1.0, fireColor.g + alpha * 0.3);

                gl_FragColor = vec4(fireColor, alpha);
            }
        `;

        // Rock vertex shader with procedural noise
        this.rockVertexShader = `
            varying vec3 vNormal;
            varying vec3 vPosition;
            varying vec2 vUv;

            // Simple noise function
            float noise(vec3 p) {
                return sin(p.x * 12.9898 + p.y * 78.233 + p.z * 37.719) * 43758.5453;
            }

            void main() {
                vNormal = normalize(normalMatrix * normal);
                vPosition = position;
                vUv = uv;

                // Add procedural displacement for rock texture
                vec3 pos = position;
                float n = noise(pos * 3.0) * 0.1;
                n += noise(pos * 6.0) * 0.05;
                n += noise(pos * 12.0) * 0.025;

                pos += normal * n;

                gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
            }
        `;

        // Rock fragment shader with realistic texturing
        this.rockFragmentShader = `
            varying vec3 vNormal;
            varying vec3 vPosition;
            varying vec2 vUv;

            uniform float time;
            uniform vec3 firePosition;
            uniform float fireIntensity;

            // Noise function for texture
            float noise(vec2 p) {
                return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);
            }

            void main() {
                // Base rock color with variation
                vec3 baseColor = vec3(0.55, 0.45, 0.33);

                // Add texture variation using noise
                float n1 = noise(vUv * 8.0);
                float n2 = noise(vUv * 16.0);
                float n3 = noise(vUv * 32.0);

                vec3 rockColor = baseColor;
                rockColor += vec3(n1 * 0.2 - 0.1);
                rockColor += vec3(n2 * 0.1 - 0.05);
                rockColor += vec3(n3 * 0.05 - 0.025);

                // Add darker cracks and crevices
                float cracks = smoothstep(0.3, 0.7, n1) * smoothstep(0.2, 0.8, n2);
                rockColor *= (0.7 + 0.3 * cracks);

                // Fire lighting effect
                float distToFire = distance(vPosition, firePosition);
                float fireEffect = fireIntensity / (1.0 + distToFire * distToFire);
                vec3 fireColor = vec3(1.0, 0.4, 0.1) * fireEffect * 0.5;

                // Basic lighting
                vec3 lightDir = normalize(vec3(1.0, 1.0, 1.0));
                float diff = max(dot(vNormal, lightDir), 0.0);

                vec3 finalColor = rockColor * (0.3 + 0.7 * diff) + fireColor;

                gl_FragColor = vec4(finalColor, 1.0);
            }
        `;
    }

    init() {
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x2a3f5f); // Dark blue-gray background
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        document.getElementById('container').appendChild(this.renderer.domElement);

        // Position camera much further back for proper view
        this.camera.position.set(0, 3, 15);
        this.camera.lookAt(0, 0, 0);

        window.addEventListener('resize', () => this.onWindowResize());
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }

    createRock() {
        // Create smaller rock geometry with higher detail for shader effects
        const geometry = new THREE.SphereGeometry(2.0, 64, 64);

        // Add noise to vertices for irregular rock shape
        const vertices = geometry.attributes.position.array;
        for (let i = 0; i < vertices.length; i += 3) {
            const vertex = new THREE.Vector3(vertices[i], vertices[i + 1], vertices[i + 2]);
            const noise = (Math.random() - 0.5) * 0.3;
            vertex.normalize().multiplyScalar(2.0 + noise);
            vertices[i] = vertex.x;
            vertices[i + 1] = vertex.y;
            vertices[i + 2] = vertex.z;
        }
        geometry.attributes.position.needsUpdate = true;
        geometry.computeVertexNormals();

        // Create shader material for realistic rock
        const material = new THREE.ShaderMaterial({
            vertexShader: this.rockVertexShader,
            fragmentShader: this.rockFragmentShader,
            uniforms: {
                time: { value: 0.0 },
                firePosition: { value: new THREE.Vector3(0, 3, 0) },
                fireIntensity: { value: 1.0 }
            }
        });

        this.rock = new THREE.Mesh(geometry, material);
        this.rock.position.set(0, -1, 0);
        this.rock.castShadow = true;
        this.rock.receiveShadow = true;
        this.scene.add(this.rock);
    }

    createFire() {
        // Create fire particle system with shader-based particles
        const particleCount = 800;
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);
        const velocities = new Float32Array(particleCount * 3);
        const lifetimes = new Float32Array(particleCount);

        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;

            // Initial positions around the rock base
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * 1.5;
            positions[i3] = Math.cos(angle) * radius;
            positions[i3 + 1] = 1.5 + Math.random() * 0.5; // Start above rock
            positions[i3 + 2] = Math.sin(angle) * radius;

            // Fire colors with more variation
            const intensity = Math.random();
            const heat = Math.random();
            colors[i3] = 0.9 + heat * 0.1; // Red
            colors[i3 + 1] = 0.1 + intensity * 0.8; // Green
            colors[i3 + 2] = heat * intensity * 0.3; // Blue

            sizes[i] = Math.random() * 0.3 + 0.1; // Smaller particles for proper scale

            // Initial velocities - upward with some spread
            velocities[i3] = (Math.random() - 0.5) * 0.08;
            velocities[i3 + 1] = Math.random() * 0.15 + 0.08; // Strong upward motion
            velocities[i3 + 2] = (Math.random() - 0.5) * 0.08;

            lifetimes[i] = Math.random() * 100 + 50;
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
        geometry.setAttribute('lifetime', new THREE.BufferAttribute(lifetimes, 1));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));

        // Store for animation
        this.fireVelocities = velocities;
        this.fireLifetimes = lifetimes;
        this.maxLifetimes = [...lifetimes];

        // Create shader material for fire
        const material = new THREE.ShaderMaterial({
            vertexShader: this.fireVertexShader,
            fragmentShader: this.fireFragmentShader,
            uniforms: {
                time: { value: 0.0 },
                pointSize: { value: 30.0 } // Reasonable point size
            },
            transparent: true,
            blending: THREE.AdditiveBlending,
            depthWrite: false,
            vertexColors: true
        });

        this.fireSystem = new THREE.Points(geometry, material);
        this.scene.add(this.fireSystem);
    }

    setupLighting() {
        // Brighter ambient light to see the rock
        const ambientLight = new THREE.AmbientLight(0x606060, 0.6);
        this.scene.add(ambientLight);

        // Fire light (orange/red)
        this.fireLight = new THREE.PointLight(0xff4500, 3, 15);
        this.fireLight.position.set(0, 2, 0);
        this.fireLight.castShadow = true;
        this.fireLight.shadow.mapSize.width = 1024;
        this.fireLight.shadow.mapSize.height = 1024;
        this.scene.add(this.fireLight);

        // Directional light for general illumination
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(5, 5, 5);
        directionalLight.castShadow = true;
        this.scene.add(directionalLight);

        // Additional fill light to illuminate the rock
        const fillLight = new THREE.DirectionalLight(0x87ceeb, 0.3);
        fillLight.position.set(-5, 3, -5);
        this.scene.add(fillLight);
    }

    setupControls() {
        // Mouse interaction
        this.mouse = new THREE.Vector2();

        document.addEventListener('mousemove', (event) => {
            this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        });

        document.addEventListener('click', () => {
            this.createExplosion();
        });
    }

    updateFire() {
        const time = this.clock.getElapsedTime();

        // Update shader uniforms
        this.fireSystem.material.uniforms.time.value = time;
        this.fireSystem.material.uniforms.pointSize.value = 1.0 + Math.sin(time * 5.0) * 0.2;

        const positions = this.fireSystem.geometry.attributes.position.array;
        const colors = this.fireSystem.geometry.attributes.color.array;
        const sizes = this.fireSystem.geometry.attributes.size.array;
        const lifetimes = this.fireSystem.geometry.attributes.lifetime.array;

        for (let i = 0; i < positions.length / 3; i++) {
            const i3 = i * 3;

            // Update positions
            positions[i3] += this.fireVelocities[i3];
            positions[i3 + 1] += this.fireVelocities[i3 + 1];
            positions[i3 + 2] += this.fireVelocities[i3 + 2];

            // Add realistic fire turbulence with time-based variation
            const turbulence = Math.sin(time * 3.0 + i * 0.1) * 0.01;
            positions[i3] += turbulence;
            positions[i3 + 2] += Math.cos(time * 2.5 + i * 0.15) * 0.01;

            // Slow down as particles rise (like real fire)
            this.fireVelocities[i3 + 1] *= 0.992;
            this.fireVelocities[i3] *= 0.998;
            this.fireVelocities[i3 + 2] *= 0.998;

            // Update lifetime
            this.fireLifetimes[i]--;
            lifetimes[i] = this.fireLifetimes[i] / this.maxLifetimes[i];

            // Reset particle if lifetime is over or too high
            if (this.fireLifetimes[i] <= 0 || positions[i3 + 1] > 8) {
                const angle = Math.random() * Math.PI * 2;
                const radius = Math.random() * 1.5;
                positions[i3] = Math.cos(angle) * radius;
                positions[i3 + 1] = 1.5 + Math.random() * 0.5;
                positions[i3 + 2] = Math.sin(angle) * radius;

                this.fireVelocities[i3] = (Math.random() - 0.5) * 0.08;
                this.fireVelocities[i3 + 1] = Math.random() * 0.15 + 0.08;
                this.fireVelocities[i3 + 2] = (Math.random() - 0.5) * 0.08;

                this.fireLifetimes[i] = this.maxLifetimes[i];
                lifetimes[i] = 1.0;
            }

            // Update color based on height and lifetime (fire gets yellower as it rises)
            const lifeRatio = this.fireLifetimes[i] / this.maxLifetimes[i];
            const height = Math.max(0, positions[i3 + 1] - 0.8);
            const intensity = Math.random();

            colors[i3] = 0.9 + intensity * 0.1; // Red with variation
            colors[i3 + 1] = 0.1 + (height * 0.6) + (intensity * 0.3); // Green increases with height
            colors[i3 + 2] = Math.min(0.9, height * 0.4 + intensity * 0.2); // Blue for white-hot tips

            // Update size based on lifetime and height
            sizes[i] = (0.4 + Math.random() * 0.4) * lifeRatio * (1.2 - height * 0.2);
        }

        this.fireSystem.geometry.attributes.position.needsUpdate = true;
        this.fireSystem.geometry.attributes.color.needsUpdate = true;
        this.fireSystem.geometry.attributes.size.needsUpdate = true;
        this.fireSystem.geometry.attributes.lifetime.needsUpdate = true;

        // Update rock shader uniforms
        if (this.rock && this.rock.material.uniforms) {
            this.rock.material.uniforms.time.value = time;
            this.rock.material.uniforms.fireIntensity.value = 1.0 + Math.sin(time * 8.0) * 0.5;
        }

        // Animate fire light with more dramatic flickering
        this.fireLight.intensity = 2.5 + Math.sin(time * 15) * 0.8;
        this.fireLight.position.y = 2 + Math.sin(time * 8) * 0.3;
    }

    createExplosion() {
        // Create temporary explosion particles
        const explosionGeometry = new THREE.BufferGeometry();
        const explosionCount = 200;
        const explosionPositions = new Float32Array(explosionCount * 3);
        const explosionColors = new Float32Array(explosionCount * 3);

        for (let i = 0; i < explosionCount; i++) {
            const i3 = i * 3;

            explosionPositions[i3] = (Math.random() - 0.5) * 0.5;
            explosionPositions[i3 + 1] = 0.5;
            explosionPositions[i3 + 2] = (Math.random() - 0.5) * 0.5;

            explosionColors[i3] = 1;
            explosionColors[i3 + 1] = Math.random();
            explosionColors[i3 + 2] = 0;
        }

        explosionGeometry.setAttribute('position', new THREE.BufferAttribute(explosionPositions, 3));
        explosionGeometry.setAttribute('color', new THREE.BufferAttribute(explosionColors, 3));

        const explosionMaterial = new THREE.PointsMaterial({
            size: 0.2,
            vertexColors: true,
            transparent: true,
            blending: THREE.AdditiveBlending
        });

        const explosion = new THREE.Points(explosionGeometry, explosionMaterial);
        this.scene.add(explosion);

        // Remove explosion after animation
        setTimeout(() => {
            this.scene.remove(explosion);
        }, 2000);
    }

    animate() {
        requestAnimationFrame(() => this.animate());

        // Update fire particles
        this.updateFire();

        // Mouse interaction with rock
        if (this.rock) {
            this.rock.rotation.y += 0.005;
            this.rock.rotation.x = this.mouse.y * 0.05;
            this.rock.rotation.z = this.mouse.x * 0.05;
        }

        // Subtle camera movement
        this.camera.position.x = Math.sin(this.clock.getElapsedTime() * 0.1) * 1;
        this.camera.position.z = 15 + Math.cos(this.clock.getElapsedTime() * 0.1) * 1;
        this.camera.position.y = 3 + Math.sin(this.clock.getElapsedTime() * 0.05) * 0.5;
        this.camera.lookAt(0, 0, 0);

        // Render the scene
        this.renderer.render(this.scene, this.camera);
    }
}

// Initialize the scene when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new FireRockScene();
});