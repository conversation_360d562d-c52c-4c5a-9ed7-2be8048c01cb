class FireRockScene {
    constructor() {
        this.scene = new THREE.Scene();
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.clock = new THREE.Clock();

        this.init();
        this.createRock();
        this.createFire();
        this.setupLighting();
        this.animate();
    }

    init() {
        // Setup renderer with better background
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x87CEEB); // Sky blue background
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        document.body.appendChild(this.renderer.domElement);

        // Position camera for good view
        this.camera.position.set(0, 1, 4);
        this.camera.lookAt(0, 0, 0);

        // Handle window resize
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });
    }

    createEmberTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');

        // Create base dark rock texture - completely opaque
        ctx.fillStyle = '#1a1a1a';
        ctx.fillRect(0, 0, 256, 256);

        // Add glowing cracks/veins
        ctx.strokeStyle = '#ff3300';
        ctx.lineWidth = 2;
        ctx.shadowColor = '#ff6600';
        ctx.shadowBlur = 8;

        // Draw random crack patterns
        for (let i = 0; i < 20; i++) {
            ctx.beginPath();
            const startX = Math.random() * 256;
            const startY = Math.random() * 256;
            ctx.moveTo(startX, startY);

            // Create branching cracks
            let x = startX;
            let y = startY;
            for (let j = 0; j < 6; j++) {
                x += (Math.random() - 0.5) * 30;
                y += (Math.random() - 0.5) * 30;
                ctx.lineTo(x, y);
            }
            ctx.stroke();
        }

        // Add hot spots/embers - no transparency
        for (let i = 0; i < 30; i++) {
            const x = Math.random() * 256;
            const y = Math.random() * 256;
            const radius = Math.random() * 6 + 2;

            const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);
            gradient.addColorStop(0, '#ffcc00');
            gradient.addColorStop(0.3, '#ff6600');
            gradient.addColorStop(0.7, '#ff3300');
            gradient.addColorStop(1, '#1a1a1a'); // Use base color instead of transparent

            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, Math.PI * 2);
            ctx.fill();
        }

        return new THREE.CanvasTexture(canvas);
    }

    createRock() {
        // Create ember texture
        const emberTexture = this.createEmberTexture();

        // Create oblong rock shape
        const geometry = new THREE.SphereGeometry(1, 32, 16);

        // Make it oblong by scaling
        geometry.scale(1.2, 0.8, 1.0);

        // Add some irregularity to make it look more natural
        const vertices = geometry.attributes.position.array;
        for (let i = 0; i < vertices.length; i += 3) {
            const vertex = new THREE.Vector3(vertices[i], vertices[i + 1], vertices[i + 2]);
            const noise = (Math.random() - 0.5) * 0.1; // Reduced noise to prevent holes
            vertex.normalize().multiplyScalar(vertex.length() + noise);
            vertices[i] = vertex.x;
            vertices[i + 1] = vertex.y;
            vertices[i + 2] = vertex.z;
        }
        geometry.attributes.position.needsUpdate = true;
        geometry.computeVertexNormals();

        // Create heated rock material with glowing embers
        const material = new THREE.MeshStandardMaterial({
            color: 0x2a1100, // Dark reddish-brown base
            map: emberTexture,
            emissive: 0x331100, // Glowing ember color
            emissiveMap: emberTexture,
            emissiveIntensity: 0.4,
            roughness: 0.8,
            metalness: 0.1,
            transparent: false, // Ensure completely opaque
            opacity: 1.0, // Full opacity
            alphaTest: 0, // No alpha testing
            side: THREE.DoubleSide // Render both sides to prevent holes
        });

        this.rock = new THREE.Mesh(geometry, material);
        this.rock.position.set(0, 0, 0);
        this.rock.castShadow = true;
        this.rock.receiveShadow = true;
        this.rockMaterial = material; // Store for animation
        this.scene.add(this.rock);
    }

    createFlameTexture() {
        // Create a more realistic flame texture
        const canvas = document.createElement('canvas');
        canvas.width = 128;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');

        // Create vertical flame gradient
        const gradient = ctx.createLinearGradient(0, 256, 0, 0);
        gradient.addColorStop(0, 'rgba(139, 0, 0, 1)');      // Dark red at base
        gradient.addColorStop(0.2, 'rgba(255, 69, 0, 1)');   // Orange red
        gradient.addColorStop(0.4, 'rgba(255, 140, 0, 1)');  // Orange
        gradient.addColorStop(0.6, 'rgba(255, 215, 0, 0.9)'); // Gold
        gradient.addColorStop(0.8, 'rgba(255, 255, 100, 0.7)'); // Yellow
        gradient.addColorStop(1, 'rgba(255, 255, 255, 0.3)'); // White hot tip

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 128, 256);

        // Add some noise for texture
        const imageData = ctx.getImageData(0, 0, 128, 256);
        const data = imageData.data;
        for (let i = 0; i < data.length; i += 4) {
            const noise = (Math.random() - 0.5) * 30;
            data[i] = Math.max(0, Math.min(255, data[i] + noise));     // R
            data[i + 1] = Math.max(0, Math.min(255, data[i + 1] + noise)); // G
            data[i + 2] = Math.max(0, Math.min(255, data[i + 2] + noise)); // B
        }
        ctx.putImageData(imageData, 0, 0);

        return new THREE.CanvasTexture(canvas);
    }

    createFire() {
        this.fireGroup = new THREE.Group();
        this.flames = [];

        // Create multiple individual flame tongues
        const flameCount = 15;
        for (let i = 0; i < flameCount; i++) {
            const flame = this.createFlame(i);
            this.flames.push(flame);
            this.fireGroup.add(flame.mesh);
        }

        // Add particle embers for extra realism
        this.createEmbers();

        // Add smoke particles
        this.createSmoke();

        this.scene.add(this.fireGroup);
    }

    createFlame(index) {
        // Create flame geometry - elongated and curved
        const flameGeometry = new THREE.ConeGeometry(0.15, 1.5, 8, 4);

        // Position vertices to create flame shape
        const positions = flameGeometry.attributes.position.array;
        for (let i = 0; i < positions.length; i += 3) {
            const y = positions[i + 1];
            const normalizedY = (y + 0.75) / 1.5; // Normalize to 0-1

            // Create flame taper and curve
            const taper = Math.pow(1 - normalizedY, 0.7);
            const curve = Math.sin(normalizedY * Math.PI * 2) * 0.1 * normalizedY;

            positions[i] *= taper; // X taper
            positions[i] += curve; // X curve
            positions[i + 2] *= taper; // Z taper
        }
        flameGeometry.attributes.position.needsUpdate = true;

        // Create flame material with realistic fire colors
        const flameMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                flameIndex: { value: index },
                baseColor: { value: new THREE.Color(0.8, 0.1, 0.0) },
                tipColor: { value: new THREE.Color(1.0, 1.0, 0.3) }
            },
            vertexShader: `
                uniform float time;
                uniform float flameIndex;
                varying vec3 vPosition;
                varying float vHeight;

                void main() {
                    vPosition = position;
                    vHeight = (position.y + 0.75) / 1.5;

                    vec3 pos = position;

                    // Add flame movement
                    float wave1 = sin(time * 3.0 + flameIndex * 0.5 + position.y * 2.0) * 0.1;
                    float wave2 = cos(time * 4.0 + flameIndex * 0.3 + position.y * 3.0) * 0.05;

                    pos.x += wave1 * vHeight;
                    pos.z += wave2 * vHeight;

                    gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
                }
            `,
            fragmentShader: `
                uniform float time;
                uniform float flameIndex;
                uniform vec3 baseColor;
                uniform vec3 tipColor;
                varying vec3 vPosition;
                varying float vHeight;

                void main() {
                    // Create flame color gradient
                    vec3 color = mix(baseColor, tipColor, vHeight);

                    // Add flickering
                    float flicker = sin(time * 8.0 + flameIndex * 2.0 + vHeight * 10.0) * 0.1 + 0.9;
                    color *= flicker;

                    // Add transparency based on height and edges
                    float alpha = (1.0 - vHeight * 0.3) * (1.0 - length(vPosition.xz) * 2.0);
                    alpha = clamp(alpha, 0.0, 1.0);

                    gl_FragColor = vec4(color, alpha);
                }
            `,
            transparent: true,
            blending: THREE.AdditiveBlending,
            depthWrite: false,
            side: THREE.DoubleSide
        });

        const flameMesh = new THREE.Mesh(flameGeometry, flameMaterial);

        // Position flame around rock
        const angle = (index / 15) * Math.PI * 2 + Math.random() * 0.5;
        const radius = 0.6 + Math.random() * 0.3;
        flameMesh.position.set(
            Math.cos(angle) * radius,
            0.2 + Math.random() * 0.2,
            Math.sin(angle) * radius
        );

        // Random rotation and scale
        flameMesh.rotation.y = angle + Math.PI;
        flameMesh.scale.setScalar(0.8 + Math.random() * 0.4);

        return {
            mesh: flameMesh,
            material: flameMaterial,
            baseHeight: flameMesh.position.y,
            phase: Math.random() * Math.PI * 2
        };
    }

    createEmbers() {
        // Create small ember particles for realism
        const emberCount = 150;
        const emberGeometry = new THREE.BufferGeometry();
        const emberPositions = new Float32Array(emberCount * 3);
        const emberColors = new Float32Array(emberCount * 3);
        const emberSizes = new Float32Array(emberCount);
        const emberVelocities = new Float32Array(emberCount * 3);
        const emberLifetimes = new Float32Array(emberCount);

        for (let i = 0; i < emberCount; i++) {
            const i3 = i * 3;

            // Start embers around the rock base
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * 0.8;
            emberPositions[i3] = Math.cos(angle) * radius;
            emberPositions[i3 + 1] = 0.3 + Math.random() * 0.2;
            emberPositions[i3 + 2] = Math.sin(angle) * radius;

            // Ember colors - hot orange to yellow
            const heat = Math.random() * 0.5 + 0.5;
            emberColors[i3] = 1.0; // Full red
            emberColors[i3 + 1] = heat * 0.8; // Orange
            emberColors[i3 + 2] = heat * 0.2; // Slight yellow

            emberSizes[i] = Math.random() * 0.05 + 0.02;

            // Upward velocities with spread
            emberVelocities[i3] = (Math.random() - 0.5) * 0.02;
            emberVelocities[i3 + 1] = Math.random() * 0.03 + 0.01;
            emberVelocities[i3 + 2] = (Math.random() - 0.5) * 0.02;

            emberLifetimes[i] = Math.random() * 100 + 50;
        }

        emberGeometry.setAttribute('position', new THREE.BufferAttribute(emberPositions, 3));
        emberGeometry.setAttribute('color', new THREE.BufferAttribute(emberColors, 3));
        emberGeometry.setAttribute('size', new THREE.BufferAttribute(emberSizes, 1));

        // Store for animation
        this.emberPositions = emberPositions;
        this.emberColors = emberColors;
        this.emberSizes = emberSizes;
        this.emberVelocities = emberVelocities;
        this.emberLifetimes = emberLifetimes;
        this.emberMaxLifetimes = [...emberLifetimes];

        const emberMaterial = new THREE.PointsMaterial({
            size: 0.1,
            vertexColors: true,
            transparent: true,
            opacity: 0.9,
            blending: THREE.AdditiveBlending,
            depthWrite: false,
            sizeAttenuation: true
        });

        this.emberParticles = new THREE.Points(emberGeometry, emberMaterial);
        this.fireGroup.add(this.emberParticles);
    }

    createSmokeTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 64;
        canvas.height = 64;
        const ctx = canvas.getContext('2d');

        // Create soft circular smoke texture
        const gradient = ctx.createRadialGradient(32, 32, 0, 32, 32, 32);
        gradient.addColorStop(0, 'rgba(100, 100, 100, 0.8)');
        gradient.addColorStop(0.3, 'rgba(80, 80, 80, 0.6)');
        gradient.addColorStop(0.6, 'rgba(60, 60, 60, 0.3)');
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 64, 64);

        return new THREE.CanvasTexture(canvas);
    }

    createSmoke() {
        const smokeTexture = this.createSmokeTexture();

        const smokeCount = 100;
        const smokeGeometry = new THREE.BufferGeometry();
        const smokePositions = new Float32Array(smokeCount * 3);
        const smokeColors = new Float32Array(smokeCount * 3);
        const smokeSizes = new Float32Array(smokeCount);
        const smokeVelocities = new Float32Array(smokeCount * 3);
        const smokeLifetimes = new Float32Array(smokeCount);

        for (let i = 0; i < smokeCount; i++) {
            const i3 = i * 3;

            // Start smoke above fire
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * 0.3;
            smokePositions[i3] = Math.cos(angle) * radius;
            smokePositions[i3 + 1] = 1.5 + Math.random() * 0.5;
            smokePositions[i3 + 2] = Math.sin(angle) * radius;

            // Smoke colors - gray to white
            const grayValue = Math.random() * 0.3 + 0.4;
            smokeColors[i3] = grayValue;
            smokeColors[i3 + 1] = grayValue;
            smokeColors[i3 + 2] = grayValue;

            smokeSizes[i] = Math.random() * 0.5 + 0.3;

            // Slower upward movement with more spread
            smokeVelocities[i3] = (Math.random() - 0.5) * 0.02;
            smokeVelocities[i3 + 1] = Math.random() * 0.02 + 0.01;
            smokeVelocities[i3 + 2] = (Math.random() - 0.5) * 0.02;

            smokeLifetimes[i] = Math.random() * 120 + 60;
        }

        smokeGeometry.setAttribute('position', new THREE.BufferAttribute(smokePositions, 3));
        smokeGeometry.setAttribute('color', new THREE.BufferAttribute(smokeColors, 3));
        smokeGeometry.setAttribute('size', new THREE.BufferAttribute(smokeSizes, 1));

        // Store for animation
        this.smokePositions = smokePositions;
        this.smokeColors = smokeColors;
        this.smokeSizes = smokeSizes;
        this.smokeVelocities = smokeVelocities;
        this.smokeLifetimes = smokeLifetimes;
        this.smokeMaxLifetimes = [...smokeLifetimes];

        const smokeMaterial = new THREE.PointsMaterial({
            map: smokeTexture,
            size: 1.0,
            vertexColors: true,
            transparent: true,
            opacity: 0.3,
            blending: THREE.NormalBlending,
            depthWrite: false,
            sizeAttenuation: true
        });

        this.smokeParticles = new THREE.Points(smokeGeometry, smokeMaterial);
        this.fireGroup.add(this.smokeParticles);
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);

        // Fire light
        this.fireLight = new THREE.PointLight(0xff4500, 2, 10);
        this.fireLight.position.set(0, 1, 0);
        this.fireLight.castShadow = true;
        this.scene.add(this.fireLight);

        // Main directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6);
        directionalLight.position.set(5, 5, 5);
        directionalLight.castShadow = true;
        this.scene.add(directionalLight);
    }

    animate() {
        requestAnimationFrame(() => this.animate());

        const time = this.clock.getElapsedTime();

        // Update flame tongues
        if (this.flames) {
            this.flames.forEach((flame, index) => {
                // Update shader time uniform
                flame.material.uniforms.time.value = time;

                // Add subtle height variation
                const heightVariation = Math.sin(time * 2 + flame.phase) * 0.1;
                flame.mesh.position.y = flame.baseHeight + heightVariation;

                // Add slight rotation for more dynamic movement
                flame.mesh.rotation.y += Math.sin(time + flame.phase) * 0.005;

                // Scale variation for breathing effect
                const scaleVariation = 1 + Math.sin(time * 3 + flame.phase) * 0.1;
                flame.mesh.scale.y = scaleVariation;
            });
        }

        // Update ember particles
        if (this.emberParticles) {
            for (let i = 0; i < this.emberPositions.length / 3; i++) {
                const i3 = i * 3;

                // Update positions
                this.emberPositions[i3] += this.emberVelocities[i3];
                this.emberPositions[i3 + 1] += this.emberVelocities[i3 + 1];
                this.emberPositions[i3 + 2] += this.emberVelocities[i3 + 2];

                // Add turbulence
                this.emberPositions[i3] += Math.sin(time * 3 + i * 0.1) * 0.003;
                this.emberPositions[i3 + 2] += Math.cos(time * 2.5 + i * 0.1) * 0.003;

                // Gravity and air resistance
                this.emberVelocities[i3 + 1] *= 0.995;
                this.emberVelocities[i3] *= 0.99;
                this.emberVelocities[i3 + 2] *= 0.99;

                // Update lifetime
                this.emberLifetimes[i]--;

                // Reset ember if lifetime is over
                if (this.emberLifetimes[i] <= 0 || this.emberPositions[i3 + 1] > 3) {
                    const angle = Math.random() * Math.PI * 2;
                    const radius = Math.random() * 0.8;
                    this.emberPositions[i3] = Math.cos(angle) * radius;
                    this.emberPositions[i3 + 1] = 0.3 + Math.random() * 0.2;
                    this.emberPositions[i3 + 2] = Math.sin(angle) * radius;

                    this.emberVelocities[i3] = (Math.random() - 0.5) * 0.02;
                    this.emberVelocities[i3 + 1] = Math.random() * 0.03 + 0.01;
                    this.emberVelocities[i3 + 2] = (Math.random() - 0.5) * 0.02;

                    this.emberLifetimes[i] = this.emberMaxLifetimes[i];
                }

                // Update colors based on lifetime
                const lifeRatio = this.emberLifetimes[i] / this.emberMaxLifetimes[i];
                this.emberColors[i3] = 1.0 * lifeRatio; // Fade red
                this.emberColors[i3 + 1] = 0.8 * lifeRatio; // Fade orange
                this.emberColors[i3 + 2] = 0.2 * lifeRatio; // Fade yellow

                // Update size
                this.emberSizes[i] = (0.02 + Math.random() * 0.03) * lifeRatio;
            }

            this.emberParticles.geometry.attributes.position.needsUpdate = true;
            this.emberParticles.geometry.attributes.color.needsUpdate = true;
            this.emberParticles.geometry.attributes.size.needsUpdate = true;
        }

        // Update smoke particles
        if (this.smokeParticles) {
            for (let i = 0; i < this.smokePositions.length / 3; i++) {
                const i3 = i * 3;

                // Update positions
                this.smokePositions[i3] += this.smokeVelocities[i3];
                this.smokePositions[i3 + 1] += this.smokeVelocities[i3 + 1];
                this.smokePositions[i3 + 2] += this.smokeVelocities[i3 + 2];

                // Add wind effect to smoke
                this.smokePositions[i3] += Math.sin(time * 0.5 + i * 0.2) * 0.01;
                this.smokePositions[i3 + 2] += Math.cos(time * 0.3 + i * 0.2) * 0.01;

                // Smoke spreads out as it rises
                this.smokeVelocities[i3] *= 1.002;
                this.smokeVelocities[i3 + 2] *= 1.002;

                // Update lifetime
                this.smokeLifetimes[i]--;

                // Reset smoke particle
                if (this.smokeLifetimes[i] <= 0 || this.smokePositions[i3 + 1] > 5) {
                    const angle = Math.random() * Math.PI * 2;
                    const radius = Math.random() * 0.3;
                    this.smokePositions[i3] = Math.cos(angle) * radius;
                    this.smokePositions[i3 + 1] = 1.5 + Math.random() * 0.5;
                    this.smokePositions[i3 + 2] = Math.sin(angle) * radius;

                    this.smokeVelocities[i3] = (Math.random() - 0.5) * 0.02;
                    this.smokeVelocities[i3 + 1] = Math.random() * 0.02 + 0.01;
                    this.smokeVelocities[i3 + 2] = (Math.random() - 0.5) * 0.02;

                    this.smokeLifetimes[i] = this.smokeMaxLifetimes[i];
                }

                // Update smoke colors (fade to lighter as it rises)
                const height = this.smokePositions[i3 + 1];
                const lifeRatio = this.smokeLifetimes[i] / this.smokeMaxLifetimes[i];
                const grayValue = Math.min(0.8, 0.4 + height * 0.1) * lifeRatio;

                this.smokeColors[i3] = grayValue;
                this.smokeColors[i3 + 1] = grayValue;
                this.smokeColors[i3 + 2] = grayValue;

                // Update size (smoke gets bigger as it rises)
                this.smokeSizes[i] = (0.3 + height * 0.2) * lifeRatio;
            }

            this.smokeParticles.geometry.attributes.position.needsUpdate = true;
            this.smokeParticles.geometry.attributes.color.needsUpdate = true;
            this.smokeParticles.geometry.attributes.size.needsUpdate = true;
        }

        // Animate fire light with more realistic flickering
        this.fireLight.intensity = 1.5 + Math.sin(time * 8) * 0.3 + Math.sin(time * 12) * 0.2;

        // Animate rock embers - make them pulse and glow
        if (this.rockMaterial) {
            // Pulsing ember intensity
            const emberPulse = 0.3 + Math.sin(time * 3) * 0.15 + Math.sin(time * 7) * 0.1;
            this.rockMaterial.emissiveIntensity = emberPulse;

            // Slight color variation for heat effect
            const heatVariation = Math.sin(time * 2) * 0.1;
            this.rockMaterial.emissive.setRGB(
                0.2 + heatVariation,
                0.067 + heatVariation * 0.5,
                0
            );
        }

        // Gentle rock rotation
        if (this.rock) {
            this.rock.rotation.y += 0.003;
        }

        // Render
        this.renderer.render(this.scene, this.camera);
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', () => {
    new FireRockScene();
});