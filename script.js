class FireRockAnimation {
    constructor() {
        this.canvas = document.getElementById('fireCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.particles = [];
        this.sparks = [];
        this.mouseX = 0;
        this.mouseY = 0;
        this.time = 0;

        this.setupCanvas();
        this.setupEventListeners();
        this.animate();
    }

    setupCanvas() {
        this.resizeCanvas();
        window.addEventListener('resize', () => this.resizeCanvas());
    }

    resizeCanvas() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
    }

    setupEventListeners() {
        document.addEventListener('mousemove', (e) => {
            const rect = this.canvas.getBoundingClientRect();
            this.mouseX = e.clientX - rect.left;
            this.mouseY = e.clientY - rect.top;
        });

        // Optional: Click anywhere on canvas for explosion effect
        this.canvas.addEventListener('click', () => {
            this.createExplosion();
        });
    }

    createParticle(x, y, vx, vy, life, color, size) {
        return {
            x: x,
            y: y,
            vx: vx,
            vy: vy,
            life: life,
            maxLife: life,
            color: color,
            size: size,
            gravity: 0.1
        };
    }

    updateParticles() {
        // Update fire particles
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const p = this.particles[i];
            p.x += p.vx;
            p.y += p.vy;
            p.vy -= p.gravity;
            p.life--;

            // Add some turbulence
            p.vx += (Math.random() - 0.5) * 0.2;
            p.vy += (Math.random() - 0.5) * 0.1;

            if (p.life <= 0) {
                this.particles.splice(i, 1);
            }
        }

        // Update sparks
        for (let i = this.sparks.length - 1; i >= 0; i--) {
            const s = this.sparks[i];
            s.x += s.vx;
            s.y += s.vy;
            s.vy += 0.2; // gravity
            s.life--;

            if (s.life <= 0) {
                this.sparks.splice(i, 1);
            }
        }
    }

    generateFireParticles() {
        const rockCenterX = this.canvas.width / 2;
        const rockCenterY = this.canvas.height / 2;

        // Generate fire particles
        for (let i = 0; i < 8; i++) {
            const angle = (Math.random() - 0.5) * Math.PI * 0.3;
            const speed = 2 + Math.random() * 3;
            const x = rockCenterX + (Math.random() - 0.5) * 60;
            const y = rockCenterY - 50;

            this.particles.push(this.createParticle(
                x, y,
                Math.sin(angle) * speed,
                -Math.abs(Math.cos(angle)) * speed,
                60 + Math.random() * 40,
                this.getFireColor(Math.random()),
                2 + Math.random() * 4
            ));
        }

        // Generate sparks occasionally
        if (Math.random() < 0.1) {
            for (let i = 0; i < 3; i++) {
                const x = rockCenterX + (Math.random() - 0.5) * 40;
                const y = rockCenterY - 30;

                this.sparks.push(this.createParticle(
                    x, y,
                    (Math.random() - 0.5) * 4,
                    -Math.random() * 6,
                    30 + Math.random() * 20,
                    '#ffaa00',
                    1 + Math.random() * 2
                ));
            }
        }
    }

    getFireColor(intensity) {
        if (intensity < 0.3) return '#ff0000';
        if (intensity < 0.6) return '#ff4500';
        if (intensity < 0.8) return '#ff8c00';
        return '#ffd700';
    }

    drawRock() {
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        const width = 120;
        const height = 80;

        // Mouse interaction offset
        const offsetX = (this.mouseX - centerX) * 0.05;
        const offsetY = (this.mouseY - centerY) * 0.05;

        this.ctx.save();
        this.ctx.translate(centerX + offsetX, centerY + offsetY);

        // Create rock gradient
        const gradient = this.ctx.createRadialGradient(-20, -20, 0, 0, 0, width);
        gradient.addColorStop(0, '#8B7355');
        gradient.addColorStop(0.3, '#6B5B47');
        gradient.addColorStop(0.7, '#4A4035');
        gradient.addColorStop(1, '#2D2520');

        // Draw main rock shape
        this.ctx.beginPath();
        this.ctx.ellipse(0, 0, width, height, 0, 0, Math.PI * 2);
        this.ctx.fillStyle = gradient;
        this.ctx.fill();

        // Add rock texture with noise
        this.ctx.globalCompositeOperation = 'multiply';
        for (let i = 0; i < 200; i++) {
            const x = (Math.random() - 0.5) * width * 2;
            const y = (Math.random() - 0.5) * height * 2;
            const distance = Math.sqrt(x*x + y*y);

            if (distance < width) {
                this.ctx.fillStyle = `rgba(${100 + Math.random() * 50}, ${80 + Math.random() * 40}, ${60 + Math.random() * 30}, 0.1)`;
                this.ctx.fillRect(x, y, 2, 2);
            }
        }

        this.ctx.globalCompositeOperation = 'source-over';

        // Add highlights
        const highlight = this.ctx.createRadialGradient(-30, -30, 0, -30, -30, 50);
        highlight.addColorStop(0, 'rgba(160, 140, 120, 0.4)');
        highlight.addColorStop(1, 'rgba(160, 140, 120, 0)');

        this.ctx.fillStyle = highlight;
        this.ctx.fill();

        // Add shadow
        this.ctx.globalCompositeOperation = 'multiply';
        const shadow = this.ctx.createRadialGradient(20, 20, 0, 20, 20, 60);
        shadow.addColorStop(0, 'rgba(0, 0, 0, 0.3)');
        shadow.addColorStop(1, 'rgba(0, 0, 0, 0)');

        this.ctx.fillStyle = shadow;
        this.ctx.fill();

        this.ctx.globalCompositeOperation = 'source-over';
        this.ctx.restore();
    }

    drawParticles() {
        this.particles.forEach(p => {
            const alpha = p.life / p.maxLife;
            this.ctx.save();

            // Create particle glow
            const gradient = this.ctx.createRadialGradient(p.x, p.y, 0, p.x, p.y, p.size * 3);
            gradient.addColorStop(0, p.color + Math.floor(alpha * 255).toString(16).padStart(2, '0'));
            gradient.addColorStop(0.5, p.color + Math.floor(alpha * 128).toString(16).padStart(2, '0'));
            gradient.addColorStop(1, p.color + '00');

            this.ctx.fillStyle = gradient;
            this.ctx.beginPath();
            this.ctx.arc(p.x, p.y, p.size * 3, 0, Math.PI * 2);
            this.ctx.fill();

            // Draw core particle
            this.ctx.fillStyle = p.color;
            this.ctx.globalAlpha = alpha;
            this.ctx.beginPath();
            this.ctx.arc(p.x, p.y, p.size, 0, Math.PI * 2);
            this.ctx.fill();

            this.ctx.restore();
        });

        // Draw sparks
        this.sparks.forEach(s => {
            const alpha = s.life / s.maxLife;
            this.ctx.save();
            this.ctx.globalAlpha = alpha;
            this.ctx.fillStyle = s.color;
            this.ctx.beginPath();
            this.ctx.arc(s.x, s.y, s.size, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();
        });
    }

    createExplosion() {
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2 - 50;

        for (let i = 0; i < 50; i++) {
            const angle = (Math.PI * 2 * i) / 50;
            const speed = 3 + Math.random() * 5;

            this.particles.push(this.createParticle(
                centerX, centerY,
                Math.cos(angle) * speed,
                Math.sin(angle) * speed,
                80 + Math.random() * 40,
                this.getFireColor(Math.random()),
                3 + Math.random() * 3
            ));
        }
    }

    animate() {
        this.time++;

        // Clear canvas with slight trail effect
        this.ctx.fillStyle = 'rgba(10, 10, 10, 0.1)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Generate new particles
        this.generateFireParticles();

        // Update all particles
        this.updateParticles();

        // Draw everything
        this.drawRock();
        this.drawParticles();

        requestAnimationFrame(() => this.animate());
    }
}

// Initialize the animation when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new FireRockAnimation();
});