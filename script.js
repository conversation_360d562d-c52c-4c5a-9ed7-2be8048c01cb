document.addEventListener('DOMContentLoaded', () => {
    const rock = document.querySelector('.rock');
    const fires = document.querySelectorAll('.fire');
    const particlesContainer = document.querySelector('.particles-container');
    const smokeContainer = document.querySelector('.smoke-container');

    // Enhanced fire animation with more realistic movement
    fires.forEach((fire, index) => {
        setInterval(() => {
            const baseHeight = [70, 55, 45, 35, 40][index] || 50;
            const randomHeight = baseHeight + Math.random() * 20 - 10;
            const randomWidth = (15 + index * 2) + Math.random() * 8 - 4;
            const randomRotation = Math.random() * 10 - 5;

            fire.style.height = `${randomHeight}px`;
            fire.style.width = `${randomWidth}px`;
            fire.style.transform = `translateX(-50%) rotate(${randomRotation}deg)`;
        }, 80 + Math.random() * 40);
    });

    // Create particle effects
    function createParticle() {
        const particle = document.createElement('div');
        particle.className = 'particle';

        const startX = Math.random() * 60 - 30; // Random position around fire
        const startY = Math.random() * 20;

        particle.style.left = `50%`;
        particle.style.bottom = `80%`;
        particle.style.transform = `translateX(${startX}px) translateY(${startY}px)`;
        particle.style.animationDelay = `${Math.random() * 0.5}s`;
        particle.style.animationDuration = `${1.5 + Math.random() * 1}s`;

        particlesContainer.appendChild(particle);

        // Remove particle after animation
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 3000);
    }

    // Create smoke effects
    function createSmoke() {
        const smoke = document.createElement('div');
        smoke.className = 'smoke';

        const startX = Math.random() * 40 - 20;
        smoke.style.left = `50%`;
        smoke.style.bottom = `85%`;
        smoke.style.transform = `translateX(${startX}px)`;
        smoke.style.animationDelay = `${Math.random() * 1}s`;
        smoke.style.animationDuration = `${3 + Math.random() * 2}s`;

        smokeContainer.appendChild(smoke);

        // Remove smoke after animation
        setTimeout(() => {
            if (smoke.parentNode) {
                smoke.parentNode.removeChild(smoke);
            }
        }, 6000);
    }

    // Generate particles and smoke continuously
    setInterval(createParticle, 200);
    setInterval(createSmoke, 800);

    // Enhanced rock response to mouse movement with subtle rotation
    document.addEventListener('mousemove', (e) => {
        const xAxis = (window.innerWidth / 2 - e.pageX) / 30;
        const yAxis = (window.innerHeight / 2 - e.pageY) / 30;
        const rotationX = yAxis / 10;
        const rotationY = xAxis / 10;

        rock.style.transform = `translate(calc(-50% + ${xAxis}px), calc(-50% + ${yAxis}px)) rotateX(${rotationX}deg) rotateY(${rotationY}deg)`;
    });

    // Button animation
    const button = document.querySelector('.cta-button');
    if (button) {
        button.addEventListener('click', () => {
            alert('Thanks for clicking! This is where you would add your conversion action.');
        });
    }
});