document.addEventListener('DOMContentLoaded', () => {
    const rock = document.querySelector('.rock');
    const fires = document.querySelectorAll('.fire');
    
    // Add some random movement to the fire
    fires.forEach(fire => {
        setInterval(() => {
            const randomHeight = 40 + Math.random() * 30;
            const randomWidth = 15 + Math.random() * 10;
            
            fire.style.height = `${randomHeight}px`;
            fire.style.width = `${randomWidth}px`;
        }, 100);
    });
    
    // Make the rock respond to mouse movement
    document.addEventListener('mousemove', (e) => {
        const xAxis = (window.innerWidth / 2 - e.pageX) / 25;
        const yAxis = (window.innerHeight / 2 - e.pageY) / 25;
        
        rock.style.transform = `translate(calc(-50% + ${xAxis}px), calc(-50% + ${yAxis}px))`;
    });
    
    // Button animation
    const button = document.querySelector('.cta-button');
    button.addEventListener('click', () => {
        alert('Thanks for clicking! This is where you would add your conversion action.');
    });
});