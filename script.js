class FireRockScene {
    constructor() {
        this.scene = new THREE.Scene();
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.clock = new THREE.Clock();

        this.init();
        this.createRock();
        this.createFire();
        this.setupLighting();
        this.animate();
    }

    init() {
        // Setup renderer
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x000000, 0); // Transparent background
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        document.body.appendChild(this.renderer.domElement);

        // Position camera for good view
        this.camera.position.set(0, 2, 6);
        this.camera.lookAt(0, 0, 0);

        // Handle window resize
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });
    }

    createRock() {
        // Create oblong rock shape
        const geometry = new THREE.SphereGeometry(1, 32, 16);

        // Make it oblong by scaling
        geometry.scale(1.2, 0.8, 1.0);

        // Add some irregularity to make it look more natural
        const vertices = geometry.attributes.position.array;
        for (let i = 0; i < vertices.length; i += 3) {
            const vertex = new THREE.Vector3(vertices[i], vertices[i + 1], vertices[i + 2]);
            const noise = (Math.random() - 0.5) * 0.15;
            vertex.normalize().multiplyScalar(vertex.length() + noise);
            vertices[i] = vertex.x;
            vertices[i + 1] = vertex.y;
            vertices[i + 2] = vertex.z;
        }
        geometry.attributes.position.needsUpdate = true;
        geometry.computeVertexNormals();

        // Create realistic rock material
        const material = new THREE.MeshLambertMaterial({
            color: 0x8B7355,
            roughness: 0.9
        });

        this.rock = new THREE.Mesh(geometry, material);
        this.rock.position.set(0, 0, 0);
        this.rock.castShadow = true;
        this.rock.receiveShadow = true;
        this.scene.add(this.rock);
    }

    createFire() {
        // Create fire particle system
        this.fireParticles = [];
        this.fireGroup = new THREE.Group();

        // Create multiple flame elements around the rock
        for (let i = 0; i < 12; i++) {
            const angle = (i / 12) * Math.PI * 2;
            const radius = 0.8 + Math.random() * 0.4;

            // Create flame geometry
            const flameGeometry = new THREE.ConeGeometry(0.1, 0.6, 8);
            const flameMaterial = new THREE.MeshBasicMaterial({
                color: new THREE.Color().setHSL(0.08 + Math.random() * 0.05, 1, 0.6),
                transparent: true,
                opacity: 0.8
            });

            const flame = new THREE.Mesh(flameGeometry, flameMaterial);
            flame.position.set(
                Math.cos(angle) * radius,
                0.8 + Math.random() * 0.2,
                Math.sin(angle) * radius
            );

            // Store animation data
            flame.userData = {
                baseY: flame.position.y,
                baseScale: 1,
                phase: Math.random() * Math.PI * 2,
                speed: 0.5 + Math.random() * 0.5
            };

            this.fireGroup.add(flame);
        }

        // Create particle system for sparks
        const particleCount = 200;
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);

        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;

            // Random positions around rock
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * 0.8;
            positions[i3] = Math.cos(angle) * radius;
            positions[i3 + 1] = 0.8 + Math.random() * 2;
            positions[i3 + 2] = Math.sin(angle) * radius;

            // Fire colors
            colors[i3] = 1; // Red
            colors[i3 + 1] = Math.random() * 0.8; // Green
            colors[i3 + 2] = 0; // Blue

            sizes[i] = Math.random() * 3 + 1;
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

        const material = new THREE.PointsMaterial({
            size: 0.05,
            vertexColors: true,
            transparent: true,
            opacity: 0.8,
            blending: THREE.AdditiveBlending
        });

        this.sparks = new THREE.Points(geometry, material);
        this.fireGroup.add(this.sparks);

        this.scene.add(this.fireGroup);
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);

        // Fire light
        this.fireLight = new THREE.PointLight(0xff4500, 2, 10);
        this.fireLight.position.set(0, 1, 0);
        this.fireLight.castShadow = true;
        this.scene.add(this.fireLight);

        // Main directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6);
        directionalLight.position.set(5, 5, 5);
        directionalLight.castShadow = true;
        this.scene.add(directionalLight);
    }

    animate() {
        requestAnimationFrame(() => this.animate());

        const time = this.clock.getElapsedTime();

        // Animate fire flames
        this.fireGroup.children.forEach((child, index) => {
            if (child.geometry && child.geometry.type === 'ConeGeometry') {
                const userData = child.userData;

                // Flickering height
                child.position.y = userData.baseY + Math.sin(time * userData.speed + userData.phase) * 0.1;

                // Flickering scale
                const scale = userData.baseScale + Math.sin(time * userData.speed * 2 + userData.phase) * 0.2;
                child.scale.set(scale, scale, scale);

                // Color variation
                const hue = 0.08 + Math.sin(time * 2 + index) * 0.02;
                child.material.color.setHSL(hue, 1, 0.6);

                // Opacity variation
                child.material.opacity = 0.6 + Math.sin(time * 3 + userData.phase) * 0.2;
            }
        });

        // Animate sparks
        if (this.sparks) {
            const positions = this.sparks.geometry.attributes.position.array;
            for (let i = 0; i < positions.length; i += 3) {
                positions[i + 1] += 0.01; // Rise up

                // Reset if too high
                if (positions[i + 1] > 3) {
                    const angle = Math.random() * Math.PI * 2;
                    const radius = Math.random() * 0.8;
                    positions[i] = Math.cos(angle) * radius;
                    positions[i + 1] = 0.8;
                    positions[i + 2] = Math.sin(angle) * radius;
                }
            }
            this.sparks.geometry.attributes.position.needsUpdate = true;
        }

        // Animate fire light
        this.fireLight.intensity = 1.5 + Math.sin(time * 5) * 0.5;

        // Gentle rock rotation
        if (this.rock) {
            this.rock.rotation.y += 0.002;
        }

        // Render
        this.renderer.render(this.scene, this.camera);
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', () => {
    new FireRockScene();
});