class FireRockScene {
    constructor() {
        this.scene = new THREE.Scene();
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.clock = new THREE.Clock();

        this.init();
        this.createRock();
        this.createFire();
        this.setupLighting();
        this.animate();
    }

    init() {
        // Setup renderer with better background
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x87CEEB); // Sky blue background
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        document.body.appendChild(this.renderer.domElement);

        // Position camera for good view
        this.camera.position.set(0, 1, 4);
        this.camera.lookAt(0, 0, 0);

        // Handle window resize
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });
    }

    createRock() {
        // Create oblong rock shape
        const geometry = new THREE.SphereGeometry(1, 32, 16);

        // Make it oblong by scaling
        geometry.scale(1.2, 0.8, 1.0);

        // Add some irregularity to make it look more natural
        const vertices = geometry.attributes.position.array;
        for (let i = 0; i < vertices.length; i += 3) {
            const vertex = new THREE.Vector3(vertices[i], vertices[i + 1], vertices[i + 2]);
            const noise = (Math.random() - 0.5) * 0.15;
            vertex.normalize().multiplyScalar(vertex.length() + noise);
            vertices[i] = vertex.x;
            vertices[i + 1] = vertex.y;
            vertices[i + 2] = vertex.z;
        }
        geometry.attributes.position.needsUpdate = true;
        geometry.computeVertexNormals();

        // Create realistic rock material
        const material = new THREE.MeshLambertMaterial({
            color: 0x8B7355,
            roughness: 0.9
        });

        this.rock = new THREE.Mesh(geometry, material);
        this.rock.position.set(0, 0, 0);
        this.rock.castShadow = true;
        this.rock.receiveShadow = true;
        this.scene.add(this.rock);
    }

    createFireTexture() {
        // Create a canvas for fire texture
        const canvas = document.createElement('canvas');
        canvas.width = 64;
        canvas.height = 64;
        const ctx = canvas.getContext('2d');

        // Create radial gradient for flame shape
        const gradient = ctx.createRadialGradient(32, 32, 0, 32, 32, 32);
        gradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
        gradient.addColorStop(0.2, 'rgba(255, 200, 0, 0.8)');
        gradient.addColorStop(0.4, 'rgba(255, 100, 0, 0.6)');
        gradient.addColorStop(0.7, 'rgba(255, 50, 0, 0.3)');
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 64, 64);

        return new THREE.CanvasTexture(canvas);
    }

    createFire() {
        this.fireGroup = new THREE.Group();

        // Create fire texture
        const fireTexture = this.createFireTexture();

        // Create realistic fire using particle system
        const particleCount = 300;
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);
        const velocities = new Float32Array(particleCount * 3);
        const lifetimes = new Float32Array(particleCount);
        const rotations = new Float32Array(particleCount);

        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;

            // Start particles around the rock base
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * 0.5;
            positions[i3] = Math.cos(angle) * radius;
            positions[i3 + 1] = 0.5 + Math.random() * 0.1;
            positions[i3 + 2] = Math.sin(angle) * radius;

            // Fire colors - proper fire gradient
            const heat = Math.random() * 0.5 + 0.5;
            colors[i3] = 1.0; // Full red
            colors[i3 + 1] = heat * 0.7; // Orange to yellow
            colors[i3 + 2] = heat * 0.1; // Slight yellow tint

            sizes[i] = Math.random() * 0.3 + 0.2;

            // Upward velocities with some spread
            velocities[i3] = (Math.random() - 0.5) * 0.015;
            velocities[i3 + 1] = Math.random() * 0.04 + 0.02;
            velocities[i3 + 2] = (Math.random() - 0.5) * 0.015;

            lifetimes[i] = Math.random() * 80 + 40;
            rotations[i] = Math.random() * Math.PI * 2;
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

        // Store for animation
        this.firePositions = positions;
        this.fireColors = colors;
        this.fireSizes = sizes;
        this.fireVelocities = velocities;
        this.fireLifetimes = lifetimes;
        this.fireRotations = rotations;
        this.maxLifetimes = [...lifetimes];

        // Create fire material with texture
        const material = new THREE.PointsMaterial({
            map: fireTexture,
            size: 0.5,
            vertexColors: true,
            transparent: true,
            opacity: 0.8,
            blending: THREE.AdditiveBlending,
            depthWrite: false,
            sizeAttenuation: true,
            alphaTest: 0.1
        });

        this.fireParticles = new THREE.Points(geometry, material);
        this.fireGroup.add(this.fireParticles);

        // Add smoke particles
        this.createSmoke();

        this.scene.add(this.fireGroup);
    }

    createSmokeTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 64;
        canvas.height = 64;
        const ctx = canvas.getContext('2d');

        // Create soft circular smoke texture
        const gradient = ctx.createRadialGradient(32, 32, 0, 32, 32, 32);
        gradient.addColorStop(0, 'rgba(100, 100, 100, 0.8)');
        gradient.addColorStop(0.3, 'rgba(80, 80, 80, 0.6)');
        gradient.addColorStop(0.6, 'rgba(60, 60, 60, 0.3)');
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 64, 64);

        return new THREE.CanvasTexture(canvas);
    }

    createSmoke() {
        const smokeTexture = this.createSmokeTexture();

        const smokeCount = 100;
        const smokeGeometry = new THREE.BufferGeometry();
        const smokePositions = new Float32Array(smokeCount * 3);
        const smokeColors = new Float32Array(smokeCount * 3);
        const smokeSizes = new Float32Array(smokeCount);
        const smokeVelocities = new Float32Array(smokeCount * 3);
        const smokeLifetimes = new Float32Array(smokeCount);

        for (let i = 0; i < smokeCount; i++) {
            const i3 = i * 3;

            // Start smoke above fire
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * 0.3;
            smokePositions[i3] = Math.cos(angle) * radius;
            smokePositions[i3 + 1] = 1.5 + Math.random() * 0.5;
            smokePositions[i3 + 2] = Math.sin(angle) * radius;

            // Smoke colors - gray to white
            const grayValue = Math.random() * 0.3 + 0.4;
            smokeColors[i3] = grayValue;
            smokeColors[i3 + 1] = grayValue;
            smokeColors[i3 + 2] = grayValue;

            smokeSizes[i] = Math.random() * 0.5 + 0.3;

            // Slower upward movement with more spread
            smokeVelocities[i3] = (Math.random() - 0.5) * 0.02;
            smokeVelocities[i3 + 1] = Math.random() * 0.02 + 0.01;
            smokeVelocities[i3 + 2] = (Math.random() - 0.5) * 0.02;

            smokeLifetimes[i] = Math.random() * 120 + 60;
        }

        smokeGeometry.setAttribute('position', new THREE.BufferAttribute(smokePositions, 3));
        smokeGeometry.setAttribute('color', new THREE.BufferAttribute(smokeColors, 3));
        smokeGeometry.setAttribute('size', new THREE.BufferAttribute(smokeSizes, 1));

        // Store for animation
        this.smokePositions = smokePositions;
        this.smokeColors = smokeColors;
        this.smokeSizes = smokeSizes;
        this.smokeVelocities = smokeVelocities;
        this.smokeLifetimes = smokeLifetimes;
        this.smokeMaxLifetimes = [...smokeLifetimes];

        const smokeMaterial = new THREE.PointsMaterial({
            map: smokeTexture,
            size: 1.0,
            vertexColors: true,
            transparent: true,
            opacity: 0.3,
            blending: THREE.NormalBlending,
            depthWrite: false,
            sizeAttenuation: true
        });

        this.smokeParticles = new THREE.Points(smokeGeometry, smokeMaterial);
        this.fireGroup.add(this.smokeParticles);
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);

        // Fire light
        this.fireLight = new THREE.PointLight(0xff4500, 2, 10);
        this.fireLight.position.set(0, 1, 0);
        this.fireLight.castShadow = true;
        this.scene.add(this.fireLight);

        // Main directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6);
        directionalLight.position.set(5, 5, 5);
        directionalLight.castShadow = true;
        this.scene.add(directionalLight);
    }

    animate() {
        requestAnimationFrame(() => this.animate());

        const time = this.clock.getElapsedTime();

        // Update fire particles
        if (this.fireParticles) {
            for (let i = 0; i < this.firePositions.length / 3; i++) {
                const i3 = i * 3;

                // Update positions with more realistic movement
                this.firePositions[i3] += this.fireVelocities[i3];
                this.firePositions[i3 + 1] += this.fireVelocities[i3 + 1];
                this.firePositions[i3 + 2] += this.fireVelocities[i3 + 2];

                // Add turbulence and wind effect
                this.firePositions[i3] += Math.sin(time * 2 + i * 0.1) * 0.005;
                this.firePositions[i3 + 2] += Math.cos(time * 1.5 + i * 0.1) * 0.005;

                // Slow down as particles rise (realistic physics)
                this.fireVelocities[i3 + 1] *= 0.992;
                this.fireVelocities[i3] *= 0.98;
                this.fireVelocities[i3 + 2] *= 0.98;

                // Update lifetime
                this.fireLifetimes[i]--;

                // Reset particle if lifetime is over
                if (this.fireLifetimes[i] <= 0 || this.firePositions[i3 + 1] > 2.5) {
                    const angle = Math.random() * Math.PI * 2;
                    const radius = Math.random() * 0.5;
                    this.firePositions[i3] = Math.cos(angle) * radius;
                    this.firePositions[i3 + 1] = 0.5 + Math.random() * 0.1;
                    this.firePositions[i3 + 2] = Math.sin(angle) * radius;

                    this.fireVelocities[i3] = (Math.random() - 0.5) * 0.015;
                    this.fireVelocities[i3 + 1] = Math.random() * 0.04 + 0.02;
                    this.fireVelocities[i3 + 2] = (Math.random() - 0.5) * 0.015;

                    this.fireLifetimes[i] = this.maxLifetimes[i];
                    this.fireRotations[i] = Math.random() * Math.PI * 2;
                }

                // Update colors based on height and lifetime
                const height = Math.max(0, this.firePositions[i3 + 1] - 0.5);
                const lifeRatio = this.fireLifetimes[i] / this.maxLifetimes[i];

                this.fireColors[i3] = 1.0; // Always full red
                this.fireColors[i3 + 1] = Math.min(0.9, height * 0.6 + 0.3 + Math.sin(time * 5 + i) * 0.1); // Dynamic orange to yellow
                this.fireColors[i3 + 2] = Math.min(0.3, height * 0.2 + Math.sin(time * 3 + i) * 0.05); // Slight yellow tint

                // Update size based on lifetime and height
                this.fireSizes[i] = (0.2 + Math.random() * 0.1) * lifeRatio * (1 + height * 0.5);

                // Update rotation
                this.fireRotations[i] += 0.02;
            }

            this.fireParticles.geometry.attributes.position.needsUpdate = true;
            this.fireParticles.geometry.attributes.color.needsUpdate = true;
            this.fireParticles.geometry.attributes.size.needsUpdate = true;
        }

        // Update smoke particles
        if (this.smokeParticles) {
            for (let i = 0; i < this.smokePositions.length / 3; i++) {
                const i3 = i * 3;

                // Update positions
                this.smokePositions[i3] += this.smokeVelocities[i3];
                this.smokePositions[i3 + 1] += this.smokeVelocities[i3 + 1];
                this.smokePositions[i3 + 2] += this.smokeVelocities[i3 + 2];

                // Add wind effect to smoke
                this.smokePositions[i3] += Math.sin(time * 0.5 + i * 0.2) * 0.01;
                this.smokePositions[i3 + 2] += Math.cos(time * 0.3 + i * 0.2) * 0.01;

                // Smoke spreads out as it rises
                this.smokeVelocities[i3] *= 1.002;
                this.smokeVelocities[i3 + 2] *= 1.002;

                // Update lifetime
                this.smokeLifetimes[i]--;

                // Reset smoke particle
                if (this.smokeLifetimes[i] <= 0 || this.smokePositions[i3 + 1] > 5) {
                    const angle = Math.random() * Math.PI * 2;
                    const radius = Math.random() * 0.3;
                    this.smokePositions[i3] = Math.cos(angle) * radius;
                    this.smokePositions[i3 + 1] = 1.5 + Math.random() * 0.5;
                    this.smokePositions[i3 + 2] = Math.sin(angle) * radius;

                    this.smokeVelocities[i3] = (Math.random() - 0.5) * 0.02;
                    this.smokeVelocities[i3 + 1] = Math.random() * 0.02 + 0.01;
                    this.smokeVelocities[i3 + 2] = (Math.random() - 0.5) * 0.02;

                    this.smokeLifetimes[i] = this.smokeMaxLifetimes[i];
                }

                // Update smoke colors (fade to lighter as it rises)
                const height = this.smokePositions[i3 + 1];
                const lifeRatio = this.smokeLifetimes[i] / this.smokeMaxLifetimes[i];
                const grayValue = Math.min(0.8, 0.4 + height * 0.1) * lifeRatio;

                this.smokeColors[i3] = grayValue;
                this.smokeColors[i3 + 1] = grayValue;
                this.smokeColors[i3 + 2] = grayValue;

                // Update size (smoke gets bigger as it rises)
                this.smokeSizes[i] = (0.3 + height * 0.2) * lifeRatio;
            }

            this.smokeParticles.geometry.attributes.position.needsUpdate = true;
            this.smokeParticles.geometry.attributes.color.needsUpdate = true;
            this.smokeParticles.geometry.attributes.size.needsUpdate = true;
        }

        // Animate fire light with more realistic flickering
        this.fireLight.intensity = 1.5 + Math.sin(time * 8) * 0.3 + Math.sin(time * 12) * 0.2;

        // Gentle rock rotation
        if (this.rock) {
            this.rock.rotation.y += 0.003;
        }

        // Render
        this.renderer.render(this.scene, this.camera);
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', () => {
    new FireRockScene();
});