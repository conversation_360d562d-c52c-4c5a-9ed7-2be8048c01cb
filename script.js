class FireRockScene {
    constructor() {
        this.scene = new THREE.Scene();
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.clock = new THREE.Clock();

        this.init();
        this.createRock();
        this.createFire();
        this.setupLighting();
        this.animate();
    }

    init() {
        // Setup renderer with better background
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x87CEEB); // Sky blue background
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        document.body.appendChild(this.renderer.domElement);

        // Position camera for good view
        this.camera.position.set(0, 1, 4);
        this.camera.lookAt(0, 0, 0);

        // Handle window resize
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });
    }

    createRock() {
        // Create oblong rock shape
        const geometry = new THREE.SphereGeometry(1, 32, 16);

        // Make it oblong by scaling
        geometry.scale(1.2, 0.8, 1.0);

        // Add some irregularity to make it look more natural
        const vertices = geometry.attributes.position.array;
        for (let i = 0; i < vertices.length; i += 3) {
            const vertex = new THREE.Vector3(vertices[i], vertices[i + 1], vertices[i + 2]);
            const noise = (Math.random() - 0.5) * 0.15;
            vertex.normalize().multiplyScalar(vertex.length() + noise);
            vertices[i] = vertex.x;
            vertices[i + 1] = vertex.y;
            vertices[i + 2] = vertex.z;
        }
        geometry.attributes.position.needsUpdate = true;
        geometry.computeVertexNormals();

        // Create realistic rock material
        const material = new THREE.MeshLambertMaterial({
            color: 0x8B7355,
            roughness: 0.9
        });

        this.rock = new THREE.Mesh(geometry, material);
        this.rock.position.set(0, 0, 0);
        this.rock.castShadow = true;
        this.rock.receiveShadow = true;
        this.scene.add(this.rock);
    }

    createFire() {
        this.fireGroup = new THREE.Group();

        // Create realistic fire using particle system
        const particleCount = 500;
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);
        const velocities = new Float32Array(particleCount * 3);
        const lifetimes = new Float32Array(particleCount);

        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;

            // Start particles around the rock base
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * 0.6;
            positions[i3] = Math.cos(angle) * radius;
            positions[i3 + 1] = 0.5 + Math.random() * 0.2;
            positions[i3 + 2] = Math.sin(angle) * radius;

            // Fire colors - proper fire gradient
            const heat = Math.random() * 0.5 + 0.5; // 0.5 to 1.0
            colors[i3] = 1.0; // Full red
            colors[i3 + 1] = heat * 0.6; // Orange to yellow
            colors[i3 + 2] = 0.0; // No blue for fire

            sizes[i] = Math.random() * 0.1 + 0.05;

            // Upward velocities with some spread
            velocities[i3] = (Math.random() - 0.5) * 0.02;
            velocities[i3 + 1] = Math.random() * 0.05 + 0.03;
            velocities[i3 + 2] = (Math.random() - 0.5) * 0.02;

            lifetimes[i] = Math.random() * 60 + 30;
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

        // Store for animation
        this.firePositions = positions;
        this.fireColors = colors;
        this.fireSizes = sizes;
        this.fireVelocities = velocities;
        this.fireLifetimes = lifetimes;
        this.maxLifetimes = [...lifetimes];

        // Create fire material with proper fire appearance
        const material = new THREE.PointsMaterial({
            size: 0.15,
            vertexColors: true,
            transparent: true,
            opacity: 0.9,
            blending: THREE.AdditiveBlending,
            depthWrite: false,
            sizeAttenuation: true
        });

        this.fireParticles = new THREE.Points(geometry, material);
        this.fireGroup.add(this.fireParticles);

        this.scene.add(this.fireGroup);
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);

        // Fire light
        this.fireLight = new THREE.PointLight(0xff4500, 2, 10);
        this.fireLight.position.set(0, 1, 0);
        this.fireLight.castShadow = true;
        this.scene.add(this.fireLight);

        // Main directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6);
        directionalLight.position.set(5, 5, 5);
        directionalLight.castShadow = true;
        this.scene.add(directionalLight);
    }

    animate() {
        requestAnimationFrame(() => this.animate());

        const time = this.clock.getElapsedTime();

        // Update fire particles
        if (this.fireParticles) {
            for (let i = 0; i < this.firePositions.length / 3; i++) {
                const i3 = i * 3;

                // Update positions
                this.firePositions[i3] += this.fireVelocities[i3];
                this.firePositions[i3 + 1] += this.fireVelocities[i3 + 1];
                this.firePositions[i3 + 2] += this.fireVelocities[i3 + 2];

                // Add turbulence
                this.firePositions[i3] += (Math.random() - 0.5) * 0.01;
                this.firePositions[i3 + 2] += (Math.random() - 0.5) * 0.01;

                // Slow down as particles rise
                this.fireVelocities[i3 + 1] *= 0.995;

                // Update lifetime
                this.fireLifetimes[i]--;

                // Reset particle if lifetime is over
                if (this.fireLifetimes[i] <= 0 || this.firePositions[i3 + 1] > 3) {
                    const angle = Math.random() * Math.PI * 2;
                    const radius = Math.random() * 0.6;
                    this.firePositions[i3] = Math.cos(angle) * radius;
                    this.firePositions[i3 + 1] = 0.5 + Math.random() * 0.2;
                    this.firePositions[i3 + 2] = Math.sin(angle) * radius;

                    this.fireVelocities[i3] = (Math.random() - 0.5) * 0.02;
                    this.fireVelocities[i3 + 1] = Math.random() * 0.05 + 0.03;
                    this.fireVelocities[i3 + 2] = (Math.random() - 0.5) * 0.02;

                    this.fireLifetimes[i] = this.maxLifetimes[i];

                    // Reset colors to fire colors
                    this.fireColors[i3] = 1.0; // Red
                    this.fireColors[i3 + 1] = Math.random() * 0.4 + 0.2; // Orange
                    this.fireColors[i3 + 2] = 0.0; // No blue
                }

                // Update colors based on height (fire gets yellower as it rises)
                const height = Math.max(0, this.firePositions[i3 + 1] - 0.5);
                const lifeRatio = this.fireLifetimes[i] / this.maxLifetimes[i];

                this.fireColors[i3] = 1.0; // Always full red
                this.fireColors[i3 + 1] = Math.min(0.8, height * 0.4 + 0.2 + Math.random() * 0.2); // Orange to yellow
                this.fireColors[i3 + 2] = 0.0; // No blue for realistic fire

                // Update size based on lifetime
                this.fireSizes[i] = (0.05 + Math.random() * 0.05) * lifeRatio;
            }

            this.fireParticles.geometry.attributes.position.needsUpdate = true;
            this.fireParticles.geometry.attributes.color.needsUpdate = true;
            this.fireParticles.geometry.attributes.size.needsUpdate = true;
        }

        // Animate fire light
        this.fireLight.intensity = 1.5 + Math.sin(time * 8) * 0.5;

        // Gentle rock rotation
        if (this.rock) {
            this.rock.rotation.y += 0.003;
        }

        // Render
        this.renderer.render(this.scene, this.camera);
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', () => {
    new FireRockScene();
});