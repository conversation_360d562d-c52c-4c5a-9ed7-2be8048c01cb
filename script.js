class FireRockScene {
    constructor() {
        this.scene = new THREE.Scene();
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.clock = new THREE.Clock();
        this.particles = [];
        this.fireParticles = [];

        this.init();
        this.createRock();
        this.createFire();
        this.setupLighting();
        this.setupControls();
        this.animate();
    }

    init() {
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x000000);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        document.getElementById('container').appendChild(this.renderer.domElement);

        this.camera.position.set(0, 2, 8);
        this.camera.lookAt(0, 0, 0);

        window.addEventListener('resize', () => this.onWindowResize());
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }

    createRock() {
        // Create rock geometry with noise for realistic shape
        const geometry = new THREE.SphereGeometry(1.5, 32, 32);

        // Add noise to vertices for irregular rock shape
        const vertices = geometry.attributes.position.array;
        for (let i = 0; i < vertices.length; i += 3) {
            const vertex = new THREE.Vector3(vertices[i], vertices[i + 1], vertices[i + 2]);
            const noise = (Math.random() - 0.5) * 0.3;
            vertex.normalize().multiplyScalar(1.5 + noise);
            vertices[i] = vertex.x;
            vertices[i + 1] = vertex.y;
            vertices[i + 2] = vertex.z;
        }
        geometry.attributes.position.needsUpdate = true;
        geometry.computeVertexNormals();

        // Create realistic rock material
        const material = new THREE.MeshLambertMaterial({
            color: 0x4a4035,
            roughness: 0.9,
            metalness: 0.1
        });

        this.rock = new THREE.Mesh(geometry, material);
        this.rock.position.set(0, -0.5, 0);
        this.rock.castShadow = true;
        this.rock.receiveShadow = true;
        this.scene.add(this.rock);
    }

    createFire() {
        // Create fire particle system
        const particleCount = 1000;
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);
        const velocities = new Float32Array(particleCount * 3);
        const lifetimes = new Float32Array(particleCount);

        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;

            // Initial positions around the rock
            positions[i3] = (Math.random() - 0.5) * 2;
            positions[i3 + 1] = 0.5 + Math.random() * 0.5;
            positions[i3 + 2] = (Math.random() - 0.5) * 2;

            // Fire colors (red to yellow)
            const intensity = Math.random();
            colors[i3] = 1; // Red
            colors[i3 + 1] = intensity; // Green
            colors[i3 + 2] = intensity * 0.3; // Blue

            sizes[i] = Math.random() * 0.1 + 0.05;

            // Initial velocities
            velocities[i3] = (Math.random() - 0.5) * 0.02;
            velocities[i3 + 1] = Math.random() * 0.05 + 0.02;
            velocities[i3 + 2] = (Math.random() - 0.5) * 0.02;

            lifetimes[i] = Math.random() * 100 + 50;
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

        // Store for animation
        this.fireVelocities = velocities;
        this.fireLifetimes = lifetimes;
        this.maxLifetimes = [...lifetimes];

        // Create fire material
        const material = new THREE.PointsMaterial({
            size: 0.1,
            vertexColors: true,
            transparent: true,
            opacity: 0.8,
            blending: THREE.AdditiveBlending,
            depthWrite: false
        });

        this.fireSystem = new THREE.Points(geometry, material);
        this.scene.add(this.fireSystem);
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        this.scene.add(ambientLight);

        // Fire light (orange/red)
        this.fireLight = new THREE.PointLight(0xff4500, 2, 10);
        this.fireLight.position.set(0, 1, 0);
        this.fireLight.castShadow = true;
        this.fireLight.shadow.mapSize.width = 1024;
        this.fireLight.shadow.mapSize.height = 1024;
        this.scene.add(this.fireLight);

        // Directional light for general illumination
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
        directionalLight.position.set(5, 5, 5);
        directionalLight.castShadow = true;
        this.scene.add(directionalLight);
    }

    setupControls() {
        // Mouse interaction
        this.mouse = new THREE.Vector2();

        document.addEventListener('mousemove', (event) => {
            this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        });

        document.addEventListener('click', () => {
            this.createExplosion();
        });
    }

    updateFire() {
        const positions = this.fireSystem.geometry.attributes.position.array;
        const colors = this.fireSystem.geometry.attributes.color.array;
        const sizes = this.fireSystem.geometry.attributes.size.array;

        for (let i = 0; i < positions.length / 3; i++) {
            const i3 = i * 3;

            // Update positions
            positions[i3] += this.fireVelocities[i3];
            positions[i3 + 1] += this.fireVelocities[i3 + 1];
            positions[i3 + 2] += this.fireVelocities[i3 + 2];

            // Add turbulence
            positions[i3] += (Math.random() - 0.5) * 0.01;
            positions[i3 + 2] += (Math.random() - 0.5) * 0.01;

            // Update lifetime
            this.fireLifetimes[i]--;

            // Reset particle if lifetime is over
            if (this.fireLifetimes[i] <= 0) {
                positions[i3] = (Math.random() - 0.5) * 2;
                positions[i3 + 1] = 0.5;
                positions[i3 + 2] = (Math.random() - 0.5) * 2;

                this.fireVelocities[i3] = (Math.random() - 0.5) * 0.02;
                this.fireVelocities[i3 + 1] = Math.random() * 0.05 + 0.02;
                this.fireVelocities[i3 + 2] = (Math.random() - 0.5) * 0.02;

                this.fireLifetimes[i] = this.maxLifetimes[i];
            }

            // Update color based on height and lifetime
            const lifeRatio = this.fireLifetimes[i] / this.maxLifetimes[i];
            const height = positions[i3 + 1];

            colors[i3] = 1; // Red
            colors[i3 + 1] = Math.min(1, height * 2) * lifeRatio; // Green
            colors[i3 + 2] = Math.max(0, height - 1) * lifeRatio; // Blue

            // Update size based on lifetime
            sizes[i] = (0.05 + Math.random() * 0.05) * lifeRatio;
        }

        this.fireSystem.geometry.attributes.position.needsUpdate = true;
        this.fireSystem.geometry.attributes.color.needsUpdate = true;
        this.fireSystem.geometry.attributes.size.needsUpdate = true;

        // Animate fire light
        this.fireLight.intensity = 1.5 + Math.sin(this.clock.getElapsedTime() * 10) * 0.5;
        this.fireLight.position.y = 1 + Math.sin(this.clock.getElapsedTime() * 5) * 0.2;
    }

    createExplosion() {
        // Create temporary explosion particles
        const explosionGeometry = new THREE.BufferGeometry();
        const explosionCount = 200;
        const explosionPositions = new Float32Array(explosionCount * 3);
        const explosionColors = new Float32Array(explosionCount * 3);

        for (let i = 0; i < explosionCount; i++) {
            const i3 = i * 3;

            explosionPositions[i3] = (Math.random() - 0.5) * 0.5;
            explosionPositions[i3 + 1] = 0.5;
            explosionPositions[i3 + 2] = (Math.random() - 0.5) * 0.5;

            explosionColors[i3] = 1;
            explosionColors[i3 + 1] = Math.random();
            explosionColors[i3 + 2] = 0;
        }

        explosionGeometry.setAttribute('position', new THREE.BufferAttribute(explosionPositions, 3));
        explosionGeometry.setAttribute('color', new THREE.BufferAttribute(explosionColors, 3));

        const explosionMaterial = new THREE.PointsMaterial({
            size: 0.2,
            vertexColors: true,
            transparent: true,
            blending: THREE.AdditiveBlending
        });

        const explosion = new THREE.Points(explosionGeometry, explosionMaterial);
        this.scene.add(explosion);

        // Remove explosion after animation
        setTimeout(() => {
            this.scene.remove(explosion);
        }, 2000);
    }

    animate() {
        requestAnimationFrame(() => this.animate());

        // Update fire particles
        this.updateFire();

        // Mouse interaction with rock
        if (this.rock) {
            this.rock.rotation.y += 0.005;
            this.rock.rotation.x = this.mouse.y * 0.1;
            this.rock.rotation.z = this.mouse.x * 0.1;
        }

        // Camera movement
        this.camera.position.x = Math.sin(this.clock.getElapsedTime() * 0.1) * 2;
        this.camera.position.z = 8 + Math.cos(this.clock.getElapsedTime() * 0.1) * 2;
        this.camera.lookAt(0, 0, 0);

        // Render the scene
        this.renderer.render(this.scene, this.camera);
    }
}

// Initialize the scene when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new FireRockScene();
});