{"clientTcpRtt": 38, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "NA", "asn": 11492, "clientAcceptEncoding": "br, gzip, deflate", "verifiedBotCategory": "", "country": "US", "isEUCountry": false, "region": "Arizona", "tlsClientCiphersSha1": "kXrN3VEKDdzz2cPKTQaKzpxVTxQ=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "d0VRreHrmEnsrwFXANTeLZz9xaiRIESlw5X8zUP2Us4=", "tlsExportedAuthenticator": {"clientFinished": "0550ada4ef9c0f9bd6158a7e7131032180918642c61cea9ba989d6ad8d5cbc4375e02e7ee5f2de938ddd3317bb9b2dcf", "clientHandshake": "4e93266ee88e5cc8f2a192d443f09080c7c06e438f88f5e3cf16e816d2bbc7a33d258c04530ab822b932d046079fb2dc", "serverHandshake": "3a6ad656abb0e49e4c61dc62f0a79214fb7b8c93523e89309ba658b2969f201bd1499b9b36bd1cd9350e81dc76ee3de8", "serverFinished": "96ce281613d4e094b2b422993689c2a2601426ed64a66d6576bfff7b4e8a42f5fa63818fdc7d2b391d494052033f3199"}, "tlsClientHelloLength": "383", "colo": "LAX", "timezone": "America/Phoenix", "longitude": "-112.46850", "latitude": "34.54002", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "86302", "city": "<PERSON>", "tlsVersion": "TLSv1.3", "regionCode": "AZ", "asOrganization": "CABLE ONE, INC.", "tlsClientExtensionsSha1Le": "u4wtEMFQBY18l3BzHAvORm+KGRw=", "tlsClientExtensionsSha1": "1eY97BUYYO8vDaTfHQywB1pcNdM=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}